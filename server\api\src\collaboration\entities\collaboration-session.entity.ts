import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { Project } from '../../projects/entities/project.entity'
import { CollaborationOperation } from './collaboration-operation.entity'

/**
 * 协作会话状态枚举
 */
export enum SessionStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  ENDED = 'ended'
}

/**
 * 协作会话实体
 * 
 * 管理多人协作的会话信息
 */
@Entity('collaboration_sessions')
export class CollaborationSession {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'varchar', length: 255 })
  name: string

  @Column({ type: 'text', nullable: true })
  description: string

  @Column({ type: 'enum', enum: SessionStatus, default: SessionStatus.ACTIVE })
  status: SessionStatus

  @Column({ name: 'project_id' })
  projectId: string

  @Column({ name: 'owner_id' })
  ownerId: string

  @Column({ name: 'max_participants', type: 'int', default: 10 })
  maxParticipants: number

  @Column({ name: 'current_participants', type: 'int', default: 0 })
  currentParticipants: number

  @Column({ type: 'json', nullable: true })
  settings: Record<string, any>

  @Column({ name: 'started_at', type: 'timestamp', nullable: true })
  startedAt: Date

  @Column({ name: 'ended_at', type: 'timestamp', nullable: true })
  endedAt: Date

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => Project, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'project_id' })
  project: Project

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'owner_id' })
  owner: User

  @OneToMany(() => CollaborationOperation, operation => operation.session)
  operations: CollaborationOperation[]
}
