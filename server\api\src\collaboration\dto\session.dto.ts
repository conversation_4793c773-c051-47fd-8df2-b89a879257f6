import { IsString, IsOptional, IsNumber, IsEnum, IsObject, IsUUI<PERSON>, <PERSON>, Max } from 'class-validator'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { SessionStatus } from '../entities/collaboration-session.entity'

/**
 * 创建协作会话DTO
 */
export class CreateSessionDto {
  @ApiProperty({ description: '会话名称' })
  @IsString()
  name: string

  @ApiPropertyOptional({ description: '会话描述' })
  @IsOptional()
  @IsString()
  description?: string

  @ApiProperty({ description: '项目ID' })
  @IsUUID()
  projectId: string

  @ApiPropertyOptional({ description: '最大参与者数量', minimum: 1, maximum: 100, default: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  maxParticipants?: number

  @ApiPropertyOptional({ description: '会话设置' })
  @IsOptional()
  @IsObject()
  settings?: Record<string, any>
}

/**
 * 更新协作会话DTO
 */
export class UpdateSessionDto {
  @ApiPropertyOptional({ description: '会话名称' })
  @IsOptional()
  @IsString()
  name?: string

  @ApiPropertyOptional({ description: '会话描述' })
  @IsOptional()
  @IsString()
  description?: string

  @ApiPropertyOptional({ description: '会话状态', enum: SessionStatus })
  @IsOptional()
  @IsEnum(SessionStatus)
  status?: SessionStatus

  @ApiPropertyOptional({ description: '最大参与者数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  maxParticipants?: number

  @ApiPropertyOptional({ description: '会话设置' })
  @IsOptional()
  @IsObject()
  settings?: Record<string, any>
}

/**
 * 会话查询DTO
 */
export class SessionQueryDto {
  @ApiPropertyOptional({ description: '项目ID' })
  @IsOptional()
  @IsUUID()
  projectId?: string

  @ApiPropertyOptional({ description: '会话状态', enum: SessionStatus })
  @IsOptional()
  @IsEnum(SessionStatus)
  status?: SessionStatus

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string

  @ApiPropertyOptional({ description: '页码', minimum: 1, default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 20 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number

  @ApiPropertyOptional({ description: '排序字段', default: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string

  @ApiPropertyOptional({ description: '排序方向', enum: ['ASC', 'DESC'], default: 'DESC' })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC'
}
