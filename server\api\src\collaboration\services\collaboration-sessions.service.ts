import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { CollaborationSession, SessionStatus } from '../entities/collaboration-session.entity'
import { User } from '../../users/entities/user.entity'
import { Project } from '../../projects/entities/project.entity'
import { CreateSessionDto, UpdateSessionDto, SessionQueryDto } from '../dto/session.dto'

/**
 * 协作会话服务
 */
@Injectable()
export class CollaborationSessionsService {
  constructor(
    @InjectRepository(CollaborationSession)
    private readonly sessionRepository: Repository<CollaborationSession>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>
  ) {}

  /**
   * 创建协作会话
   */
  async createSession(createSessionDto: CreateSessionDto, userId: string): Promise<CollaborationSession> {
    // 验证项目是否存在
    const project = await this.projectRepository.findOne({ where: { id: createSessionDto.projectId } })
    if (!project) {
      throw new NotFoundException('项目不存在')
    }

    // 验证用户是否有权限创建会话
    // TODO: 添加权限检查逻辑

    const session = this.sessionRepository.create({
      ...createSessionDto,
      ownerId: userId,
      status: SessionStatus.ACTIVE,
      currentParticipants: 1,
      startedAt: new Date()
    })

    return await this.sessionRepository.save(session)
  }

  /**
   * 获取协作会话列表
   */
  async getSessions(query: SessionQueryDto, userId: string) {
    const queryBuilder = this.sessionRepository.createQueryBuilder('session')
      .leftJoinAndSelect('session.project', 'project')
      .leftJoinAndSelect('session.owner', 'owner')

    // 权限过滤：只能看到自己参与的会话或公开的会话
    // TODO: 实现更复杂的权限逻辑

    if (query.projectId) {
      queryBuilder.andWhere('session.projectId = :projectId', { projectId: query.projectId })
    }

    if (query.status) {
      queryBuilder.andWhere('session.status = :status', { status: query.status })
    }

    if (query.search) {
      queryBuilder.andWhere(
        '(session.name LIKE :search OR session.description LIKE :search)',
        { search: `%${query.search}%` }
      )
    }

    // 分页
    const page = query.page || 1
    const limit = query.limit || 20
    const skip = (page - 1) * limit

    queryBuilder.skip(skip).take(limit)

    // 排序
    const sortBy = query.sortBy || 'createdAt'
    const sortOrder = query.sortOrder || 'DESC'
    queryBuilder.orderBy(`session.${sortBy}`, sortOrder)

    const [sessions, total] = await queryBuilder.getManyAndCount()

    return {
      data: sessions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 获取协作会话详情
   */
  async getSession(id: string, userId: string): Promise<CollaborationSession> {
    const session = await this.sessionRepository.findOne({
      where: { id },
      relations: ['project', 'owner']
    })

    if (!session) {
      throw new NotFoundException('会话不存在')
    }

    // TODO: 检查用户是否有权限查看此会话

    return session
  }

  /**
   * 更新协作会话
   */
  async updateSession(id: string, updateSessionDto: UpdateSessionDto, userId: string): Promise<CollaborationSession> {
    const session = await this.getSession(id, userId)

    // 检查权限：只有会话所有者可以更新
    if (session.ownerId !== userId) {
      throw new ForbiddenException('只有会话所有者可以更新会话')
    }

    Object.assign(session, updateSessionDto)
    return await this.sessionRepository.save(session)
  }

  /**
   * 删除协作会话
   */
  async deleteSession(id: string, userId: string): Promise<void> {
    const session = await this.getSession(id, userId)

    // 检查权限：只有会话所有者可以删除
    if (session.ownerId !== userId) {
      throw new ForbiddenException('只有会话所有者可以删除会话')
    }

    // 软删除：设置状态为结束
    session.status = SessionStatus.ENDED
    session.endedAt = new Date()
    await this.sessionRepository.save(session)
  }

  /**
   * 加入协作会话
   */
  async joinSession(id: string, userId: string): Promise<{ success: boolean, message: string }> {
    const session = await this.getSession(id, userId)

    // 检查会话状态
    if (session.status !== SessionStatus.ACTIVE) {
      throw new ConflictException('会话未激活')
    }

    // 检查参与者数量限制
    if (session.currentParticipants >= session.maxParticipants) {
      throw new ConflictException('会话已满')
    }

    // TODO: 检查用户是否已经在会话中
    // TODO: 添加参与者记录

    // 更新参与者数量
    session.currentParticipants += 1
    await this.sessionRepository.save(session)

    return { success: true, message: '成功加入会话' }
  }

  /**
   * 离开协作会话
   */
  async leaveSession(id: string, userId: string): Promise<{ success: boolean, message: string }> {
    const session = await this.getSession(id, userId)

    // TODO: 检查用户是否在会话中
    // TODO: 移除参与者记录

    // 更新参与者数量
    if (session.currentParticipants > 0) {
      session.currentParticipants -= 1
      await this.sessionRepository.save(session)
    }

    return { success: true, message: '成功离开会话' }
  }

  /**
   * 获取会话参与者
   */
  async getParticipants(id: string, userId: string) {
    const session = await this.getSession(id, userId)

    // TODO: 实现参与者查询逻辑
    // 这里需要创建参与者实体和关联关系

    return {
      sessionId: id,
      participants: [],
      totalCount: session.currentParticipants
    }
  }

  /**
   * 检查会话权限
   */
  private async checkSessionPermission(sessionId: string, userId: string, action: string): Promise<boolean> {
    // TODO: 实现权限检查逻辑
    return true
  }

  /**
   * 获取用户参与的会话
   */
  async getUserSessions(userId: string, query: SessionQueryDto) {
    const queryBuilder = this.sessionRepository.createQueryBuilder('session')
      .leftJoinAndSelect('session.project', 'project')
      .leftJoinAndSelect('session.owner', 'owner')
      .where('session.ownerId = :userId', { userId })

    // TODO: 添加参与者关联查询
    // .orWhere('participant.userId = :userId', { userId })

    if (query.status) {
      queryBuilder.andWhere('session.status = :status', { status: query.status })
    }

    const page = query.page || 1
    const limit = query.limit || 20
    const skip = (page - 1) * limit

    queryBuilder.skip(skip).take(limit)
    queryBuilder.orderBy('session.createdAt', 'DESC')

    const [sessions, total] = await queryBuilder.getManyAndCount()

    return {
      data: sessions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }
}
