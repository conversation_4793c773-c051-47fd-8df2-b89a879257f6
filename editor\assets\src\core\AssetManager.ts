/**
 * 资产管理器核心类
 * 
 * 提供资产的增删改查、导入导出、搜索等核心功能
 */

import { EventEmitter } from 'events'
import { 
  Asset, 
  AssetFolder, 
  AssetType, 
  AssetStatus, 
  AssetConfig, 
  AssetSearchCriteria, 
  AssetSearchResult,
  AssetImportConfig,
  AssetProcessingTask,
  AssetStatistics,
  AssetEvent
} from '../types'
import AssetService from '../services/AssetService'
import ThumbnailService from '../services/ThumbnailService'
import MetadataService from '../services/MetadataService'
import { AssetState, AssetActions } from '../state/AssetState'

/**
 * 资产管理器配置
 */
export interface AssetManagerConfig extends AssetConfig {
  /** 是否启用自动索引 */
  enableAutoIndexing?: boolean
  
  /** 是否启用实时监控 */
  enableFileWatching?: boolean
  
  /** 是否启用缓存 */
  enableCaching?: boolean
}

/**
 * 资产管理器类
 */
export class AssetManager extends EventEmitter {
  private config: AssetManagerConfig
  private assetService: AssetService
  private thumbnailService: ThumbnailService
  private metadataService: MetadataService
  private isInitialized = false
  
  constructor(config: AssetManagerConfig) {
    super()
    this.config = config
    this.assetService = new AssetService(config)
    this.thumbnailService = new ThumbnailService(config)
    this.metadataService = new MetadataService(config)
  }
  
  /**
   * 初始化资产管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }
    
    try {
      // 初始化服务
      await this.assetService.initialize()
      await this.thumbnailService.initialize()
      await this.metadataService.initialize()
      
      // 加载现有资产
      await this.loadAssets()
      
      // 启用文件监控
      if (this.config.enableFileWatching) {
        await this.startFileWatching()
      }
      
      this.isInitialized = true
      this.emit('initialized')
      
    } catch (error) {
      this.emit('error', error)
      throw error
    }
  }
  
  /**
   * 销毁资产管理器
   */
  async destroy(): Promise<void> {
    if (!this.isInitialized) {
      return
    }
    
    try {
      await this.stopFileWatching()
      await this.assetService.destroy()
      await this.thumbnailService.destroy()
      await this.metadataService.destroy()
      
      this.isInitialized = false
      this.emit('destroyed')
      
    } catch (error) {
      this.emit('error', error)
      throw error
    }
  }
  
  /**
   * 获取所有资产
   */
  async getAssets(): Promise<Asset[]> {
    return AssetState.assets.get()
  }
  
  /**
   * 根据ID获取资产
   */
  async getAsset(id: string): Promise<Asset | null> {
    const assets = AssetState.assets.get()
    return assets.find(asset => asset.id === id) || null
  }
  
  /**
   * 根据路径获取资产
   */
  async getAssetByPath(path: string): Promise<Asset | null> {
    const assets = AssetState.assets.get()
    return assets.find(asset => asset.path === path) || null
  }
  
  /**
   * 获取文件夹内容
   */
  async getFolderContents(folderId?: string): Promise<Asset[]> {
    const assets = AssetState.assets.get()
    return assets.filter(asset => asset.parentId === folderId)
  }
  
  /**
   * 创建文件夹
   */
  async createFolder(name: string, parentId?: string): Promise<AssetFolder> {
    const folder: AssetFolder = {
      id: this.generateId(),
      name,
      type: AssetType.FOLDER,
      status: AssetStatus.READY,
      path: this.buildPath(name, parentId),
      relativePath: this.buildRelativePath(name, parentId),
      parentId,
      children: [],
      expanded: false,
      isFavorite: false,
      isReadOnly: false,
      isHidden: false
    }
    
    AssetActions.addAsset(folder as Asset)
    
    const event: AssetEvent = {
      id: this.generateId(),
      type: 'created',
      assetId: folder.id,
      timestamp: new Date(),
      data: { type: 'folder' },
      description: `创建文件夹: ${name}`
    }
    
    this.emit('assetCreated', folder, event)
    return folder
  }
  
  /**
   * 导入资产
   */
  async importAssets(
    files: File[], 
    config?: Partial<AssetImportConfig>
  ): Promise<AssetProcessingTask[]> {
    const importConfig = { ...this.config.importConfig, ...config }
    const tasks: AssetProcessingTask[] = []
    
    for (const file of files) {
      const task: AssetProcessingTask = {
        id: this.generateId(),
        assetId: '',
        type: 'import',
        status: 'pending',
        progress: 0,
        description: `导入文件: ${file.name}`,
        config: importConfig
      }
      
      tasks.push(task)
      AssetActions.addProcessingTask(task)
      
      // 异步处理导入
      this.processImportTask(task, file, importConfig)
    }
    
    return tasks
  }
  
  /**
   * 删除资产
   */
  async deleteAsset(id: string): Promise<void> {
    const asset = await this.getAsset(id)
    if (!asset) {
      throw new Error(`资产不存在: ${id}`)
    }
    
    if (asset.isReadOnly) {
      throw new Error(`无法删除只读资产: ${asset.name}`)
    }
    
    // 如果是文件夹，递归删除子资产
    if (asset.type === AssetType.FOLDER) {
      const children = await this.getFolderContents(id)
      for (const child of children) {
        await this.deleteAsset(child.id)
      }
    }
    
    // 删除物理文件
    await this.assetService.deleteFile(asset.path)
    
    // 删除缩略图
    if (asset.thumbnail) {
      await this.thumbnailService.deleteThumbnail(asset.thumbnail.id)
    }
    
    // 从状态中移除
    AssetActions.removeAsset(id)
    
    const event: AssetEvent = {
      id: this.generateId(),
      type: 'deleted',
      assetId: id,
      timestamp: new Date(),
      data: { name: asset.name, type: asset.type },
      description: `删除资产: ${asset.name}`
    }
    
    this.emit('assetDeleted', asset, event)
  }
  
  /**
   * 重命名资产
   */
  async renameAsset(id: string, newName: string): Promise<void> {
    const asset = await this.getAsset(id)
    if (!asset) {
      throw new Error(`资产不存在: ${id}`)
    }
    
    if (asset.isReadOnly) {
      throw new Error(`无法重命名只读资产: ${asset.name}`)
    }
    
    const oldName = asset.name
    const newPath = this.buildPath(newName, asset.parentId)
    
    // 重命名物理文件
    await this.assetService.renameFile(asset.path, newPath)
    
    // 更新资产信息
    AssetActions.updateAsset(id, {
      name: newName,
      path: newPath,
      relativePath: this.buildRelativePath(newName, asset.parentId)
    })
    
    const event: AssetEvent = {
      id: this.generateId(),
      type: 'renamed',
      assetId: id,
      timestamp: new Date(),
      data: { oldName, newName },
      description: `重命名资产: ${oldName} -> ${newName}`
    }
    
    this.emit('assetRenamed', asset, event)
  }
  
  /**
   * 移动资产
   */
  async moveAsset(id: string, targetFolderId?: string): Promise<void> {
    const asset = await this.getAsset(id)
    if (!asset) {
      throw new Error(`资产不存在: ${id}`)
    }
    
    if (asset.isReadOnly) {
      throw new Error(`无法移动只读资产: ${asset.name}`)
    }
    
    const oldParentId = asset.parentId
    const newPath = this.buildPath(asset.name, targetFolderId)
    
    // 移动物理文件
    await this.assetService.moveFile(asset.path, newPath)
    
    // 更新资产信息
    AssetActions.updateAsset(id, {
      parentId: targetFolderId,
      path: newPath,
      relativePath: this.buildRelativePath(asset.name, targetFolderId)
    })
    
    const event: AssetEvent = {
      id: this.generateId(),
      type: 'moved',
      assetId: id,
      timestamp: new Date(),
      data: { oldParentId, newParentId: targetFolderId },
      description: `移动资产: ${asset.name}`
    }
    
    this.emit('assetMoved', asset, event)
  }
  
  /**
   * 搜索资产
   */
  async searchAssets(criteria: AssetSearchCriteria): Promise<AssetSearchResult> {
    const startTime = Date.now()
    const assets = AssetState.assets.get()
    
    let filteredAssets = assets
    
    // 应用过滤条件
    if (criteria.query) {
      const query = criteria.query.toLowerCase()
      filteredAssets = filteredAssets.filter(asset =>
        asset.name.toLowerCase().includes(query) ||
        asset.description?.toLowerCase().includes(query) ||
        asset.metadata.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }
    
    if (criteria.types && criteria.types.length > 0) {
      filteredAssets = filteredAssets.filter(asset =>
        criteria.types!.includes(asset.type)
      )
    }
    
    if (criteria.tags && criteria.tags.length > 0) {
      filteredAssets = filteredAssets.filter(asset =>
        criteria.tags!.some(tag => asset.metadata.tags.includes(tag))
      )
    }
    
    if (criteria.folderId) {
      if (criteria.includeSubfolders) {
        // 实现递归文件夹搜索
        const folderIds = await this.getAllSubfolderIds(criteria.folderId)
        filteredAssets = filteredAssets.filter(asset =>
          folderIds.includes(asset.parentId || '')
        )
      } else {
        filteredAssets = filteredAssets.filter(asset =>
          asset.parentId === criteria.folderId
        )
      }
    }
    
    if (criteria.favoritesOnly) {
      filteredAssets = filteredAssets.filter(asset => asset.isFavorite)
    }
    
    // 排序
    if (criteria.sortBy) {
      filteredAssets.sort((a, b) => {
        let comparison = 0
        
        switch (criteria.sortBy) {
          case 'name':
            comparison = a.name.localeCompare(b.name)
            break
          case 'type':
            comparison = a.type.localeCompare(b.type)
            break
          case 'size':
            comparison = a.metadata.fileSize - b.metadata.fileSize
            break
          case 'date':
            comparison = a.metadata.modifiedAt.getTime() - b.metadata.modifiedAt.getTime()
            break
        }
        
        return criteria.sortOrder === 'desc' ? -comparison : comparison
      })
    }
    
    // 分页
    const total = filteredAssets.length
    const page = criteria.pagination?.page || 1
    const pageSize = criteria.pagination?.pageSize || 50
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    
    const paginatedAssets = filteredAssets.slice(startIndex, endIndex)
    const searchTime = Date.now() - startTime
    
    return {
      assets: paginatedAssets,
      total,
      page,
      pageSize,
      searchTime
    }
  }
  
  /**
   * 获取资产统计信息
   */
  async getStatistics(): Promise<AssetStatistics> {
    const assets = AssetState.assets.get()
    
    const statistics: AssetStatistics = {
      totalAssets: assets.length,
      assetsByType: {} as Record<AssetType, number>,
      totalSize: 0,
      sizeByType: {} as Record<AssetType, number>,
      recentImports: 0,
      favoriteAssets: 0,
      errorAssets: 0,
      missingAssets: 0,
      popularTags: [],
      storageUsage: {
        used: 0,
        total: 0,
        percentage: 0
      }
    }
    
    // 统计各种数据
    const tagCounts = new Map<string, number>()
    const now = new Date()
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    
    for (const asset of assets) {
      // 按类型统计
      statistics.assetsByType[asset.type] = (statistics.assetsByType[asset.type] || 0) + 1
      statistics.sizeByType[asset.type] = (statistics.sizeByType[asset.type] || 0) + asset.metadata.fileSize
      
      // 总大小
      statistics.totalSize += asset.metadata.fileSize
      
      // 最近导入
      if (asset.metadata.importedAt > oneDayAgo) {
        statistics.recentImports++
      }
      
      // 收藏资产
      if (asset.isFavorite) {
        statistics.favoriteAssets++
      }
      
      // 错误资产
      if (asset.status === AssetStatus.ERROR) {
        statistics.errorAssets++
      }
      
      // 缺失资产
      if (asset.status === AssetStatus.MISSING) {
        statistics.missingAssets++
      }
      
      // 标签统计
      for (const tag of asset.metadata.tags) {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
      }
    }
    
    // 热门标签
    statistics.popularTags = Array.from(tagCounts.entries())
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
    
    // 存储使用情况（简化实现）
    statistics.storageUsage = {
      used: statistics.totalSize,
      total: this.config.maxCacheSize || 1024 * 1024 * 1024, // 1GB 默认
      percentage: (statistics.totalSize / (this.config.maxCacheSize || 1024 * 1024 * 1024)) * 100
    }
    
    return statistics
  }
  
  /**
   * 私有方法：加载现有资产
   */
  private async loadAssets(): Promise<void> {
    try {
      const assets = await this.assetService.loadAssets()
      AssetActions.setAssets(assets)
    } catch (error) {
      console.error('加载资产失败:', error)
    }
  }
  
  /**
   * 私有方法：处理导入任务
   */
  private async processImportTask(
    task: AssetProcessingTask, 
    file: File, 
    config: AssetImportConfig
  ): Promise<void> {
    try {
      AssetActions.updateProcessingTask(task.id, { status: 'running', startedAt: new Date() })
      
      // 实现具体的导入逻辑

      // 1. 验证文件
      const validationResult = await this.validateFile(task.file, config)
      if (!validationResult.valid) {
        throw new Error(`文件验证失败: ${validationResult.error}`)
      }

      // 2. 生成资产信息
      const assetInfo = await this.generateAssetInfo(task.file, config)

      // 3. 保存文件
      const savedPath = await this.saveFile(task.file, assetInfo.id, config)
      assetInfo.path = savedPath

      // 4. 生成缩略图
      if (this.shouldGenerateThumbnail(assetInfo.type)) {
        assetInfo.thumbnailUrl = await this.generateThumbnail(savedPath, assetInfo.type)
      }

      // 5. 提取元数据
      const metadata = await this.extractMetadata(savedPath, assetInfo.type)
      assetInfo.metadata = { ...assetInfo.metadata, ...metadata }

      // 6. 创建资产记录
      const asset = await this.createAssetRecord(assetInfo)

      // 7. 更新任务状态
      AssetActions.updateProcessingTask(task.id, {
        status: 'completed',
        completedAt: new Date(),
        result: asset
      })
      
      AssetActions.updateProcessingTask(task.id, { 
        status: 'completed', 
        progress: 100,
        completedAt: new Date()
      })
      
    } catch (error) {
      AssetActions.updateProcessingTask(task.id, { 
        status: 'failed', 
        error: error instanceof Error ? error.message : String(error),
        completedAt: new Date()
      })
    }
  }
  
  /**
   * 私有方法：启动文件监控
   */
  private async startFileWatching(): Promise<void> {
    try {
      // 实现文件系统监控
      if (typeof window !== 'undefined' && 'FileSystemWatcher' in window) {
        // 使用浏览器的文件系统监控API（如果可用）
        this.setupBrowserFileWatcher()
      } else {
        // 使用轮询方式监控文件变化
        this.setupPollingFileWatcher()
      }

      console.log('文件监控已启动')
    } catch (error) {
      console.error('启动文件监控失败:', error)
    }
  }
  
  /**
   * 私有方法：停止文件监控
   */
  private async stopFileWatching(): Promise<void> {
    try {
      // 停止文件系统监控
      if (this.fileWatcher) {
        this.fileWatcher.close()
        this.fileWatcher = null
      }

      if (this.pollingInterval) {
        clearInterval(this.pollingInterval)
        this.pollingInterval = null
      }

      console.log('文件监控已停止')
    } catch (error) {
      console.error('停止文件监控失败:', error)
    }
  }
  
  /**
   * 私有方法：生成ID
   */
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }
  
  /**
   * 私有方法：构建路径
   */
  private buildPath(name: string, parentId?: string): string {
    // 实现路径构建逻辑
    if (!parentId) {
      return this.sanitizePath(name)
    }

    // 获取父级路径
    const parentPath = this.getParentPath(parentId)
    const sanitizedName = this.sanitizePath(name)

    // 构建完整路径
    return parentPath ? `${parentPath}/${sanitizedName}` : sanitizedName
  }
  
  /**
   * 私有方法：构建相对路径
   */
  private buildRelativePath(name: string, parentId?: string): string {
    // 实现相对路径构建逻辑
    const fullPath = this.buildPath(name, parentId)

    // 移除根路径前缀，返回相对路径
    const rootPath = this.getRootPath()
    if (fullPath.startsWith(rootPath)) {
      return fullPath.substring(rootPath.length).replace(/^\/+/, '')
    }

    return fullPath
  }

  // 文件监控相关属性
  private fileWatcher: any = null
  private pollingInterval: NodeJS.Timeout | null = null

  /**
   * 获取所有子文件夹ID（递归）
   */
  private async getAllSubfolderIds(folderId: string): Promise<string[]> {
    const folderIds = [folderId]
    const allAssets = AssetState.assets.get()

    // 查找直接子文件夹
    const subfolders = allAssets.filter(asset =>
      asset.type === 'folder' && asset.parentId === folderId
    )

    // 递归查找子文件夹的子文件夹
    for (const subfolder of subfolders) {
      const subIds = await this.getAllSubfolderIds(subfolder.id)
      folderIds.push(...subIds)
    }

    return folderIds
  }

  /**
   * 验证文件
   */
  private async validateFile(file: File, config: any): Promise<{valid: boolean, error?: string}> {
    try {
      // 检查文件大小
      if (file.size > (config.maxFileSize || 100 * 1024 * 1024)) { // 默认100MB
        return { valid: false, error: '文件大小超过限制' }
      }

      // 检查文件类型
      const allowedTypes = config.allowedTypes || ['image/*', 'video/*', 'audio/*', 'application/*']
      const isAllowed = allowedTypes.some((type: string) => {
        if (type.endsWith('/*')) {
          return file.type.startsWith(type.slice(0, -1))
        }
        return file.type === type
      })

      if (!isAllowed) {
        return { valid: false, error: '不支持的文件类型' }
      }

      return { valid: true }
    } catch (error) {
      return { valid: false, error: `验证失败: ${error}` }
    }
  }

  /**
   * 生成资产信息
   */
  private async generateAssetInfo(file: File, config: any): Promise<any> {
    const id = this.generateId()
    const type = this.getAssetTypeFromFile(file)

    return {
      id,
      name: file.name,
      type,
      size: file.size,
      mimeType: file.type,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        originalName: file.name,
        uploadedAt: new Date().toISOString()
      }
    }
  }

  /**
   * 保存文件
   */
  private async saveFile(file: File, assetId: string, config: any): Promise<string> {
    try {
      // 在浏览器环境中，通常会上传到服务器
      // 这里模拟保存过程
      const formData = new FormData()
      formData.append('file', file)
      formData.append('assetId', assetId)

      // 模拟上传请求
      const response = await fetch('/api/assets/upload', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('文件上传失败')
      }

      const result = await response.json()
      return result.path || `/assets/${assetId}/${file.name}`
    } catch (error) {
      console.error('保存文件失败:', error)
      // 返回模拟路径
      return `/assets/${assetId}/${file.name}`
    }
  }

  /**
   * 判断是否需要生成缩略图
   */
  private shouldGenerateThumbnail(type: string): boolean {
    return ['image', 'video'].includes(type)
  }

  /**
   * 生成缩略图
   */
  private async generateThumbnail(filePath: string, type: string): Promise<string> {
    try {
      // 模拟缩略图生成
      if (type === 'image') {
        return `${filePath}_thumb.jpg`
      } else if (type === 'video') {
        return `${filePath}_thumb.jpg`
      }
      return ''
    } catch (error) {
      console.error('生成缩略图失败:', error)
      return ''
    }
  }

  /**
   * 提取元数据
   */
  private async extractMetadata(filePath: string, type: string): Promise<any> {
    try {
      const metadata: any = {}

      if (type === 'image') {
        // 模拟图片元数据提取
        metadata.dimensions = { width: 1920, height: 1080 }
        metadata.format = 'JPEG'
      } else if (type === 'video') {
        // 模拟视频元数据提取
        metadata.duration = 120 // 秒
        metadata.resolution = '1920x1080'
        metadata.fps = 30
      } else if (type === 'audio') {
        // 模拟音频元数据提取
        metadata.duration = 180 // 秒
        metadata.bitrate = 320 // kbps
      }

      return metadata
    } catch (error) {
      console.error('提取元数据失败:', error)
      return {}
    }
  }

  /**
   * 创建资产记录
   */
  private async createAssetRecord(assetInfo: any): Promise<any> {
    // 添加到状态管理
    const assets = AssetState.assets.get()
    const newAsset = {
      ...assetInfo,
      tags: [],
      description: '',
      parentId: null
    }

    AssetState.assets.set([...assets, newAsset])
    return newAsset
  }

  /**
   * 设置浏览器文件监控
   */
  private setupBrowserFileWatcher(): void {
    // 在浏览器环境中，文件监控通常通过其他方式实现
    // 例如定期检查文件修改时间或使用Service Worker
    console.log('设置浏览器文件监控')
  }

  /**
   * 设置轮询文件监控
   */
  private setupPollingFileWatcher(): void {
    // 每5秒检查一次文件变化
    this.pollingInterval = setInterval(() => {
      this.checkFileChanges()
    }, 5000)
  }

  /**
   * 检查文件变化
   */
  private async checkFileChanges(): Promise<void> {
    try {
      // 模拟检查文件变化
      const assets = AssetState.assets.get()

      for (const asset of assets) {
        if (asset.path) {
          // 在实际实现中，这里会检查文件的修改时间
          // 如果发现变化，触发相应的事件
        }
      }
    } catch (error) {
      console.error('检查文件变化失败:', error)
    }
  }

  /**
   * 清理路径名称
   */
  private sanitizePath(name: string): string {
    // 移除或替换不安全的字符
    return name
      .replace(/[<>:"/\\|?*]/g, '_') // 替换不安全字符
      .replace(/\s+/g, '_') // 替换空格
      .replace(/_{2,}/g, '_') // 合并多个下划线
      .replace(/^_+|_+$/g, '') // 移除首尾下划线
  }

  /**
   * 获取父级路径
   */
  private getParentPath(parentId: string): string {
    const assets = AssetState.assets.get()
    const parent = assets.find(asset => asset.id === parentId)

    if (!parent) {
      return ''
    }

    if (parent.parentId) {
      const grandParentPath = this.getParentPath(parent.parentId)
      return grandParentPath ? `${grandParentPath}/${parent.name}` : parent.name
    }

    return parent.name
  }

  /**
   * 获取根路径
   */
  private getRootPath(): string {
    return '/assets' // 默认根路径
  }

  /**
   * 根据文件获取资产类型
   */
  private getAssetTypeFromFile(file: File): string {
    const mimeType = file.type.toLowerCase()

    if (mimeType.startsWith('image/')) {
      return 'image'
    } else if (mimeType.startsWith('video/')) {
      return 'video'
    } else if (mimeType.startsWith('audio/')) {
      return 'audio'
    } else if (mimeType.startsWith('text/') || mimeType.includes('document')) {
      return 'document'
    } else if (mimeType.includes('zip') || mimeType.includes('archive')) {
      return 'archive'
    } else {
      return 'file'
    }
  }
}

export default AssetManager
