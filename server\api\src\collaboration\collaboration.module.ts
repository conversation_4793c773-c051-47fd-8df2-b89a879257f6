import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigModule } from '@nestjs/config'

// 协作服务
import { CollaborationService } from './services/collaboration.service'
import { CollaborationSessionsService } from './services/collaboration-sessions.service'
import { CollaborationOperationsService } from './services/collaboration-operations.service'
import { CollaborationConflictsService } from './services/collaboration-conflicts.service'
import { RealTimeCollaborationService } from './services/real-time-collaboration.service'
import { OperationalTransformService } from './services/operational-transform.service'
import { ConflictResolutionService } from './services/conflict-resolution.service'

// 协作控制器
import { CollaborationSessionsController } from './controllers/collaboration-sessions.controller'
import { CollaborationOperationsController } from './controllers/collaboration-operations.controller'
import { CollaborationConflictsController } from './controllers/collaboration-conflicts.controller'

// 协作相关实体
import { CollaborationSession } from './entities/collaboration-session.entity'
import { CollaborationOperation } from './entities/collaboration-operation.entity'
import { CollaborationConflict } from './entities/collaboration-conflict.entity'
import { CollaborationLock } from './entities/collaboration-lock.entity'

// 基础实体
import { User } from '../users/entities/user.entity'
import { Project } from '../projects/entities/project.entity'

// 注意：以下模块引用需要确认是否存在
// import { UsersModule } from '../users/users.module'
// import { ProjectsModule } from '../projects/projects.module'
// import { ScenesModule } from '../scenes/scenes.module'
// import { NotificationModule } from '../notifications/notification.module'

/**
 * 协作功能模块
 *
 * 支持多人教学协作的完整功能，包括：
 * - 实时协作会话管理 ✅
 * - 协作操作同步和冲突解决 ✅
 * - 版本冲突处理和自动解决 ✅
 * - 操作转换算法 ✅
 * - 实时WebSocket通信 ✅
 * - 操作历史记录 ✅
 * - 资源锁定机制 ✅
 * - 智能冲突检测和解决建议 ✅
 *
 * 功能特性：
 * - RESTful API接口
 * - WebSocket实时通信
 * - 操作转换算法
 * - 智能冲突解决
 * - 完整的权限控制
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      User,
      Project,
      CollaborationSession,
      CollaborationOperation,
      CollaborationConflict,
      CollaborationLock
    ])
    // 相关模块依赖（根据需要启用）
    // UsersModule,        // 用户管理模块
    // ProjectsModule,     // 项目管理模块
    // ScenesModule,       // 场景管理模块
    // NotificationModule  // 通知模块
  ],
  controllers: [
    CollaborationSessionsController,
    CollaborationOperationsController,
    CollaborationConflictsController
  ],
  providers: [
    CollaborationService,
    CollaborationSessionsService,
    CollaborationOperationsService,
    CollaborationConflictsService,
    RealTimeCollaborationService,
    OperationalTransformService,
    ConflictResolutionService
  ],
  exports: [
    CollaborationService,
    CollaborationSessionsService,
    CollaborationOperationsService,
    CollaborationConflictsService,
    RealTimeCollaborationService,
    OperationalTransformService,
    ConflictResolutionService
  ]
})
export class CollaborationModule {}
