import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Request,
  HttpStatus,
  HttpException
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard'
import { CollaborationSessionsService } from '../services/collaboration-sessions.service'
import { CreateSessionDto, UpdateSessionDto, SessionQueryDto } from '../dto/session.dto'

/**
 * 协作会话控制器
 * 
 * 管理协作会话的CRUD操作
 */
@ApiTags('协作会话')
@Controller('collaboration/sessions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CollaborationSessionsController {
  constructor(
    private readonly sessionsService: CollaborationSessionsService
  ) {}

  /**
   * 创建协作会话
   */
  @Post()
  @ApiOperation({ summary: '创建协作会话' })
  @ApiResponse({ status: 201, description: '会话创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 403, description: '权限不足' })
  async createSession(
    @Body() createSessionDto: CreateSessionDto,
    @Request() req: any
  ) {
    try {
      const userId = req.user.id
      return await this.sessionsService.createSession(createSessionDto, userId)
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST)
    }
  }

  /**
   * 获取协作会话列表
   */
  @Get()
  @ApiOperation({ summary: '获取协作会话列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSessions(
    @Query() query: SessionQueryDto,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.sessionsService.getSessions(query, userId)
  }

  /**
   * 获取协作会话详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取协作会话详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async getSession(
    @Param('id') id: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.sessionsService.getSession(id, userId)
  }

  /**
   * 更新协作会话
   */
  @Put(':id')
  @ApiOperation({ summary: '更新协作会话' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  @ApiResponse({ status: 403, description: '权限不足' })
  async updateSession(
    @Param('id') id: string,
    @Body() updateSessionDto: UpdateSessionDto,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.sessionsService.updateSession(id, updateSessionDto, userId)
  }

  /**
   * 删除协作会话
   */
  @Delete(':id')
  @ApiOperation({ summary: '删除协作会话' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  @ApiResponse({ status: 403, description: '权限不足' })
  async deleteSession(
    @Param('id') id: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.sessionsService.deleteSession(id, userId)
  }

  /**
   * 加入协作会话
   */
  @Post(':id/join')
  @ApiOperation({ summary: '加入协作会话' })
  @ApiResponse({ status: 200, description: '加入成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  @ApiResponse({ status: 403, description: '权限不足或会话已满' })
  async joinSession(
    @Param('id') id: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.sessionsService.joinSession(id, userId)
  }

  /**
   * 离开协作会话
   */
  @Post(':id/leave')
  @ApiOperation({ summary: '离开协作会话' })
  @ApiResponse({ status: 200, description: '离开成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async leaveSession(
    @Param('id') id: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.sessionsService.leaveSession(id, userId)
  }

  /**
   * 获取会话参与者
   */
  @Get(':id/participants')
  @ApiOperation({ summary: '获取会话参与者' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async getParticipants(
    @Param('id') id: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.sessionsService.getParticipants(id, userId)
  }
}
