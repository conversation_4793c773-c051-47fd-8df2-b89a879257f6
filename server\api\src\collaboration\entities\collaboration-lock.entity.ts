import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { CollaborationSession } from './collaboration-session.entity'

/**
 * 锁类型枚举
 */
export enum LockType {
  EXCLUSIVE = 'exclusive',
  SHARED = 'shared',
  READ_ONLY = 'read_only'
}

/**
 * 锁状态枚举
 */
export enum LockStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  RELEASED = 'released'
}

/**
 * 协作锁实体
 * 
 * 管理协作过程中的资源锁定
 */
@Entity('collaboration_locks')
export class CollaborationLock {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ name: 'session_id' })
  sessionId: string

  @Column({ name: 'user_id' })
  userId: string

  @Column({ type: 'enum', enum: LockType, default: LockType.EXCLUSIVE })
  type: LockType

  @Column({ type: 'enum', enum: LockStatus, default: LockStatus.ACTIVE })
  status: LockStatus

  @Column({ name: 'resource_type', type: 'varchar', length: 100 })
  resourceType: string

  @Column({ name: 'resource_id', type: 'varchar', length: 255 })
  resourceId: string

  @Column({ name: 'resource_path', type: 'text', nullable: true })
  resourcePath: string

  @Column({ type: 'text', nullable: true })
  reason: string

  @Column({ name: 'expires_at', type: 'timestamp', nullable: true })
  expiresAt: Date

  @Column({ name: 'auto_release', type: 'boolean', default: true })
  autoRelease: boolean

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => CollaborationSession, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'session_id' })
  session: CollaborationSession

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User
}
