import { Injectable, Logger } from '@nestjs/common'
import { WebSocketGateway, WebSocketServer, SubscribeMessage, OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets'
import { Server, Socket } from 'socket.io'
import { CollaborationSessionsService } from './collaboration-sessions.service'
import { CollaborationOperationsService } from './collaboration-operations.service'
import { CollaborationConflictsService } from './collaboration-conflicts.service'

/**
 * 实时协作服务
 * 
 * 处理WebSocket连接和实时协作功能
 */
@Injectable()
@WebSocketGateway({
  namespace: '/collaboration',
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
})
export class RealTimeCollaborationService implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server

  private readonly logger = new Logger(RealTimeCollaborationService.name)
  private readonly userSessions = new Map<string, Set<string>>() // userId -> sessionIds
  private readonly sessionUsers = new Map<string, Set<string>>() // sessionId -> userIds
  private readonly socketUsers = new Map<string, string>() // socketId -> userId

  constructor(
    private readonly sessionsService: CollaborationSessionsService,
    private readonly operationsService: CollaborationOperationsService,
    private readonly conflictsService: CollaborationConflictsService
  ) {}

  /**
   * 客户端连接
   */
  async handleConnection(client: Socket) {
    this.logger.log(`客户端连接: ${client.id}`)
    
    // TODO: 验证用户身份
    const userId = await this.authenticateUser(client)
    if (!userId) {
      client.disconnect()
      return
    }

    this.socketUsers.set(client.id, userId)
    this.logger.log(`用户 ${userId} 已连接`)
  }

  /**
   * 客户端断开连接
   */
  async handleDisconnect(client: Socket) {
    const userId = this.socketUsers.get(client.id)
    if (userId) {
      this.logger.log(`用户 ${userId} 断开连接`)
      
      // 从所有会话中移除用户
      const userSessionIds = this.userSessions.get(userId) || new Set()
      for (const sessionId of userSessionIds) {
        await this.leaveSession(client, { sessionId })
      }
      
      this.socketUsers.delete(client.id)
    }
  }

  /**
   * 加入协作会话
   */
  @SubscribeMessage('join-session')
  async joinSession(client: Socket, data: { sessionId: string }) {
    const userId = this.socketUsers.get(client.id)
    if (!userId) {
      client.emit('error', { message: '用户未认证' })
      return
    }

    try {
      // 验证会话权限
      const session = await this.sessionsService.getSession(data.sessionId, userId)
      
      // 加入Socket房间
      client.join(data.sessionId)
      
      // 更新用户会话映射
      if (!this.userSessions.has(userId)) {
        this.userSessions.set(userId, new Set())
      }
      this.userSessions.get(userId)!.add(data.sessionId)
      
      if (!this.sessionUsers.has(data.sessionId)) {
        this.sessionUsers.set(data.sessionId, new Set())
      }
      this.sessionUsers.get(data.sessionId)!.add(userId)
      
      // 通知其他用户
      client.to(data.sessionId).emit('user-joined', {
        userId,
        sessionId: data.sessionId,
        timestamp: new Date()
      })
      
      // 发送当前会话状态
      const participants = Array.from(this.sessionUsers.get(data.sessionId) || [])
      client.emit('session-joined', {
        sessionId: data.sessionId,
        participants,
        timestamp: new Date()
      })
      
      this.logger.log(`用户 ${userId} 加入会话 ${data.sessionId}`)
    } catch (error) {
      client.emit('error', { message: error.message })
    }
  }

  /**
   * 离开协作会话
   */
  @SubscribeMessage('leave-session')
  async leaveSession(client: Socket, data: { sessionId: string }) {
    const userId = this.socketUsers.get(client.id)
    if (!userId) return

    try {
      // 离开Socket房间
      client.leave(data.sessionId)
      
      // 更新用户会话映射
      this.userSessions.get(userId)?.delete(data.sessionId)
      this.sessionUsers.get(data.sessionId)?.delete(userId)
      
      // 通知其他用户
      client.to(data.sessionId).emit('user-left', {
        userId,
        sessionId: data.sessionId,
        timestamp: new Date()
      })
      
      this.logger.log(`用户 ${userId} 离开会话 ${data.sessionId}`)
    } catch (error) {
      this.logger.error(`用户离开会话失败: ${error.message}`)
    }
  }

  /**
   * 发送操作
   */
  @SubscribeMessage('send-operation')
  async sendOperation(client: Socket, data: any) {
    const userId = this.socketUsers.get(client.id)
    if (!userId) {
      client.emit('error', { message: '用户未认证' })
      return
    }

    try {
      // 创建操作记录
      const operation = await this.operationsService.createOperation(data, userId)
      
      // 广播操作给会话中的其他用户
      client.to(data.sessionId).emit('operation-received', {
        operation,
        timestamp: new Date()
      })
      
      // 确认操作已发送
      client.emit('operation-sent', {
        operationId: operation.id,
        timestamp: new Date()
      })
      
      this.logger.log(`用户 ${userId} 发送操作到会话 ${data.sessionId}`)
    } catch (error) {
      client.emit('operation-error', { 
        message: error.message,
        originalData: data
      })
    }
  }

  /**
   * 同步操作
   */
  @SubscribeMessage('sync-operations')
  async syncOperations(client: Socket, data: { sessionId: string, lastOperationId?: string }) {
    const userId = this.socketUsers.get(client.id)
    if (!userId) {
      client.emit('error', { message: '用户未认证' })
      return
    }

    try {
      const result = await this.operationsService.syncOperations(
        data.sessionId, 
        data.lastOperationId, 
        userId
      )
      
      client.emit('operations-synced', result)
    } catch (error) {
      client.emit('sync-error', { message: error.message })
    }
  }

  /**
   * 发送光标位置
   */
  @SubscribeMessage('cursor-position')
  async sendCursorPosition(client: Socket, data: { sessionId: string, position: any }) {
    const userId = this.socketUsers.get(client.id)
    if (!userId) return

    // 广播光标位置给会话中的其他用户
    client.to(data.sessionId).emit('cursor-updated', {
      userId,
      position: data.position,
      timestamp: new Date()
    })
  }

  /**
   * 发送选择区域
   */
  @SubscribeMessage('selection-changed')
  async sendSelection(client: Socket, data: { sessionId: string, selection: any }) {
    const userId = this.socketUsers.get(client.id)
    if (!userId) return

    // 广播选择区域给会话中的其他用户
    client.to(data.sessionId).emit('selection-updated', {
      userId,
      selection: data.selection,
      timestamp: new Date()
    })
  }

  /**
   * 发送用户状态
   */
  @SubscribeMessage('user-status')
  async sendUserStatus(client: Socket, data: { sessionId: string, status: string }) {
    const userId = this.socketUsers.get(client.id)
    if (!userId) return

    // 广播用户状态给会话中的其他用户
    client.to(data.sessionId).emit('user-status-updated', {
      userId,
      status: data.status,
      timestamp: new Date()
    })
  }

  /**
   * 广播冲突通知
   */
  async broadcastConflict(sessionId: string, conflict: any) {
    this.server.to(sessionId).emit('conflict-detected', {
      conflict,
      timestamp: new Date()
    })
  }

  /**
   * 广播冲突解决
   */
  async broadcastConflictResolution(sessionId: string, conflictId: string, resolution: any) {
    this.server.to(sessionId).emit('conflict-resolved', {
      conflictId,
      resolution,
      timestamp: new Date()
    })
  }

  /**
   * 获取会话在线用户
   */
  getSessionUsers(sessionId: string): string[] {
    return Array.from(this.sessionUsers.get(sessionId) || [])
  }

  /**
   * 获取用户参与的会话
   */
  getUserSessions(userId: string): string[] {
    return Array.from(this.userSessions.get(userId) || [])
  }

  /**
   * 验证用户身份
   */
  private async authenticateUser(client: Socket): Promise<string | null> {
    try {
      // TODO: 实现JWT token验证
      const token = client.handshake.auth.token || client.handshake.headers.authorization
      if (!token) {
        return null
      }
      
      // 解析token获取用户ID
      // const decoded = jwt.verify(token, process.env.JWT_SECRET)
      // return decoded.userId
      
      // 临时返回模拟用户ID
      return 'user-' + Math.random().toString(36).substr(2, 9)
    } catch (error) {
      this.logger.error(`用户认证失败: ${error.message}`)
      return null
    }
  }
}
