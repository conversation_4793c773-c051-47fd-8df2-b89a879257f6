import { Injectable, Logger } from '@nestjs/common'
import { CollaborationOperation, OperationType } from '../entities/collaboration-operation.entity'

/**
 * 操作转换服务
 * 
 * 实现操作转换算法，解决并发编辑冲突
 */
@Injectable()
export class OperationalTransformService {
  private readonly logger = new Logger(OperationalTransformService.name)

  /**
   * 转换操作
   * 
   * @param operation1 第一个操作
   * @param operation2 第二个操作
   * @returns 转换后的操作对
   */
  transform(operation1: CollaborationOperation, operation2: CollaborationOperation): {
    operation1Prime: CollaborationOperation,
    operation2Prime: CollaborationOperation
  } {
    // 检查操作是否针对同一目标
    if (operation1.targetId !== operation2.targetId || operation1.targetType !== operation2.targetType) {
      // 不同目标，无需转换
      return {
        operation1Prime: operation1,
        operation2Prime: operation2
      }
    }

    // 根据操作类型进行转换
    return this.transformByType(operation1, operation2)
  }

  /**
   * 根据操作类型进行转换
   */
  private transformByType(op1: CollaborationOperation, op2: CollaborationOperation): {
    operation1Prime: CollaborationOperation,
    operation2Prime: CollaborationOperation
  } {
    const type1 = op1.type
    const type2 = op2.type

    // 文本编辑操作转换
    if (this.isTextOperation(op1) && this.isTextOperation(op2)) {
      return this.transformTextOperations(op1, op2)
    }

    // 对象属性操作转换
    if (this.isPropertyOperation(op1) && this.isPropertyOperation(op2)) {
      return this.transformPropertyOperations(op1, op2)
    }

    // 列表操作转换
    if (this.isListOperation(op1) && this.isListOperation(op2)) {
      return this.transformListOperations(op1, op2)
    }

    // 创建/删除操作转换
    if (type1 === OperationType.CREATE || type1 === OperationType.DELETE ||
        type2 === OperationType.CREATE || type2 === OperationType.DELETE) {
      return this.transformCreateDeleteOperations(op1, op2)
    }

    // 默认情况：无转换
    return {
      operation1Prime: op1,
      operation2Prime: op2
    }
  }

  /**
   * 转换文本操作
   */
  private transformTextOperations(op1: CollaborationOperation, op2: CollaborationOperation): {
    operation1Prime: CollaborationOperation,
    operation2Prime: CollaborationOperation
  } {
    const pos1 = op1.data.position || 0
    const pos2 = op2.data.position || 0
    const len1 = op1.data.length || 0
    const len2 = op2.data.length || 0

    let newOp1 = { ...op1 }
    let newOp2 = { ...op2 }

    // 插入 vs 插入
    if (op1.type === OperationType.UPDATE && op2.type === OperationType.UPDATE &&
        op1.data.action === 'insert' && op2.data.action === 'insert') {
      
      if (pos1 <= pos2) {
        // op1在op2之前或同位置，op2位置需要后移
        newOp2 = {
          ...op2,
          data: {
            ...op2.data,
            position: pos2 + len1
          }
        }
      } else {
        // op2在op1之前，op1位置需要后移
        newOp1 = {
          ...op1,
          data: {
            ...op1.data,
            position: pos1 + len2
          }
        }
      }
    }

    // 插入 vs 删除
    else if (op1.data.action === 'insert' && op2.data.action === 'delete') {
      if (pos1 <= pos2) {
        // 插入在删除之前，删除位置后移
        newOp2 = {
          ...op2,
          data: {
            ...op2.data,
            position: pos2 + len1
          }
        }
      } else if (pos1 <= pos2 + len2) {
        // 插入在删除范围内，插入位置调整到删除开始位置
        newOp1 = {
          ...op1,
          data: {
            ...op1.data,
            position: pos2
          }
        }
      } else {
        // 插入在删除之后，插入位置前移
        newOp1 = {
          ...op1,
          data: {
            ...op1.data,
            position: pos1 - len2
          }
        }
      }
    }

    // 删除 vs 插入
    else if (op1.data.action === 'delete' && op2.data.action === 'insert') {
      if (pos2 <= pos1) {
        // 插入在删除之前，删除位置后移
        newOp1 = {
          ...op1,
          data: {
            ...op1.data,
            position: pos1 + len2
          }
        }
      } else if (pos2 <= pos1 + len1) {
        // 插入在删除范围内，插入位置调整到删除开始位置
        newOp2 = {
          ...op2,
          data: {
            ...op2.data,
            position: pos1
          }
        }
      } else {
        // 插入在删除之后，插入位置前移
        newOp2 = {
          ...op2,
          data: {
            ...op2.data,
            position: pos2 - len1
          }
        }
      }
    }

    // 删除 vs 删除
    else if (op1.data.action === 'delete' && op2.data.action === 'delete') {
      if (pos1 + len1 <= pos2) {
        // op1完全在op2之前，op2位置前移
        newOp2 = {
          ...op2,
          data: {
            ...op2.data,
            position: pos2 - len1
          }
        }
      } else if (pos2 + len2 <= pos1) {
        // op2完全在op1之前，op1位置前移
        newOp1 = {
          ...op1,
          data: {
            ...op1.data,
            position: pos1 - len2
          }
        }
      } else {
        // 删除范围重叠，需要调整删除长度和位置
        const overlapStart = Math.max(pos1, pos2)
        const overlapEnd = Math.min(pos1 + len1, pos2 + len2)
        const overlapLen = overlapEnd - overlapStart

        if (pos1 <= pos2) {
          newOp1 = {
            ...op1,
            data: {
              ...op1.data,
              length: len1 - overlapLen
            }
          }
          newOp2 = {
            ...op2,
            data: {
              ...op2.data,
              position: pos1,
              length: len2 - overlapLen
            }
          }
        } else {
          newOp1 = {
            ...op1,
            data: {
              ...op1.data,
              position: pos2,
              length: len1 - overlapLen
            }
          }
          newOp2 = {
            ...op2,
            data: {
              ...op2.data,
              length: len2 - overlapLen
            }
          }
        }
      }
    }

    return {
      operation1Prime: newOp1,
      operation2Prime: newOp2
    }
  }

  /**
   * 转换属性操作
   */
  private transformPropertyOperations(op1: CollaborationOperation, op2: CollaborationOperation): {
    operation1Prime: CollaborationOperation,
    operation2Prime: CollaborationOperation
  } {
    const prop1 = op1.data.property
    const prop2 = op2.data.property

    // 不同属性，无需转换
    if (prop1 !== prop2) {
      return {
        operation1Prime: op1,
        operation2Prime: op2
      }
    }

    // 相同属性，后执行的操作优先
    if (op1.operationOrder < op2.operationOrder) {
      // op2优先，op1变为无操作
      return {
        operation1Prime: this.createNoOpOperation(op1),
        operation2Prime: op2
      }
    } else {
      // op1优先，op2变为无操作
      return {
        operation1Prime: op1,
        operation2Prime: this.createNoOpOperation(op2)
      }
    }
  }

  /**
   * 转换列表操作
   */
  private transformListOperations(op1: CollaborationOperation, op2: CollaborationOperation): {
    operation1Prime: CollaborationOperation,
    operation2Prime: CollaborationOperation
  } {
    const index1 = op1.data.index || 0
    const index2 = op2.data.index || 0

    let newOp1 = { ...op1 }
    let newOp2 = { ...op2 }

    // 插入 vs 插入
    if (op1.data.action === 'insert' && op2.data.action === 'insert') {
      if (index1 <= index2) {
        newOp2 = {
          ...op2,
          data: {
            ...op2.data,
            index: index2 + 1
          }
        }
      } else {
        newOp1 = {
          ...op1,
          data: {
            ...op1.data,
            index: index1 + 1
          }
        }
      }
    }

    // 插入 vs 删除
    else if (op1.data.action === 'insert' && op2.data.action === 'delete') {
      if (index1 <= index2) {
        newOp2 = {
          ...op2,
          data: {
            ...op2.data,
            index: index2 + 1
          }
        }
      } else {
        newOp1 = {
          ...op1,
          data: {
            ...op1.data,
            index: index1 - 1
          }
        }
      }
    }

    // 删除 vs 插入
    else if (op1.data.action === 'delete' && op2.data.action === 'insert') {
      if (index2 <= index1) {
        newOp1 = {
          ...op1,
          data: {
            ...op1.data,
            index: index1 + 1
          }
        }
      } else {
        newOp2 = {
          ...op2,
          data: {
            ...op2.data,
            index: index2 - 1
          }
        }
      }
    }

    // 删除 vs 删除
    else if (op1.data.action === 'delete' && op2.data.action === 'delete') {
      if (index1 === index2) {
        // 删除同一项，后执行的变为无操作
        if (op1.operationOrder < op2.operationOrder) {
          newOp2 = this.createNoOpOperation(op2)
        } else {
          newOp1 = this.createNoOpOperation(op1)
        }
      } else if (index1 < index2) {
        newOp2 = {
          ...op2,
          data: {
            ...op2.data,
            index: index2 - 1
          }
        }
      } else {
        newOp1 = {
          ...op1,
          data: {
            ...op1.data,
            index: index1 - 1
          }
        }
      }
    }

    return {
      operation1Prime: newOp1,
      operation2Prime: newOp2
    }
  }

  /**
   * 转换创建/删除操作
   */
  private transformCreateDeleteOperations(op1: CollaborationOperation, op2: CollaborationOperation): {
    operation1Prime: CollaborationOperation,
    operation2Prime: CollaborationOperation
  } {
    // 创建 vs 创建：冲突，后执行的失败
    if (op1.type === OperationType.CREATE && op2.type === OperationType.CREATE) {
      if (op1.operationOrder < op2.operationOrder) {
        return {
          operation1Prime: op1,
          operation2Prime: this.createNoOpOperation(op2)
        }
      } else {
        return {
          operation1Prime: this.createNoOpOperation(op1),
          operation2Prime: op2
        }
      }
    }

    // 删除 vs 删除：冲突，后执行的失败
    if (op1.type === OperationType.DELETE && op2.type === OperationType.DELETE) {
      if (op1.operationOrder < op2.operationOrder) {
        return {
          operation1Prime: op1,
          operation2Prime: this.createNoOpOperation(op2)
        }
      } else {
        return {
          operation1Prime: this.createNoOpOperation(op1),
          operation2Prime: op2
        }
      }
    }

    // 创建 vs 删除：删除失败
    if (op1.type === OperationType.CREATE && op2.type === OperationType.DELETE) {
      return {
        operation1Prime: op1,
        operation2Prime: this.createNoOpOperation(op2)
      }
    }

    // 删除 vs 创建：创建失败
    if (op1.type === OperationType.DELETE && op2.type === OperationType.CREATE) {
      return {
        operation1Prime: op1,
        operation2Prime: this.createNoOpOperation(op2)
      }
    }

    return {
      operation1Prime: op1,
      operation2Prime: op2
    }
  }

  /**
   * 创建无操作
   */
  private createNoOpOperation(originalOp: CollaborationOperation): CollaborationOperation {
    return {
      ...originalOp,
      type: OperationType.UPDATE,
      data: { action: 'noop' },
      metadata: {
        ...originalOp.metadata,
        transformedToNoop: true,
        originalType: originalOp.type
      }
    }
  }

  /**
   * 检查是否为文本操作
   */
  private isTextOperation(op: CollaborationOperation): boolean {
    return op.targetType === 'text' || 
           (op.data.action && ['insert', 'delete', 'replace'].includes(op.data.action))
  }

  /**
   * 检查是否为属性操作
   */
  private isPropertyOperation(op: CollaborationOperation): boolean {
    return op.data.property !== undefined
  }

  /**
   * 检查是否为列表操作
   */
  private isListOperation(op: CollaborationOperation): boolean {
    return op.targetType === 'list' || 
           (op.data.index !== undefined && op.data.action && ['insert', 'delete', 'move'].includes(op.data.action))
  }

  /**
   * 批量转换操作序列
   */
  transformSequence(operations: CollaborationOperation[]): CollaborationOperation[] {
    if (operations.length <= 1) {
      return operations
    }

    const result = [operations[0]]
    
    for (let i = 1; i < operations.length; i++) {
      let currentOp = operations[i]
      
      // 与之前的所有操作进行转换
      for (let j = 0; j < result.length; j++) {
        const transformed = this.transform(result[j], currentOp)
        result[j] = transformed.operation1Prime
        currentOp = transformed.operation2Prime
      }
      
      result.push(currentOp)
    }
    
    return result
  }
}
