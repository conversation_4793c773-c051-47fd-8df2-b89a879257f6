import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { CollaborationConflict, ConflictStatus, ConflictType } from '../entities/collaboration-conflict.entity'
import { CollaborationSession } from '../entities/collaboration-session.entity'
import { CollaborationOperation } from '../entities/collaboration-operation.entity'
import { User } from '../../users/entities/user.entity'
import { ResolveConflictDto, ConflictQueryDto } from '../dto/conflict.dto'

/**
 * 协作冲突服务
 */
@Injectable()
export class CollaborationConflictsService {
  constructor(
    @InjectRepository(CollaborationConflict)
    private readonly conflictRepository: Repository<CollaborationConflict>,
    @InjectRepository(CollaborationSession)
    private readonly sessionRepository: Repository<CollaborationSession>,
    @InjectRepository(CollaborationOperation)
    private readonly operationRepository: Repository<CollaborationOperation>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}

  /**
   * 获取会话冲突列表
   */
  async getSessionConflicts(sessionId: string, query: ConflictQueryDto, userId: string) {
    // 验证用户权限
    await this.checkSessionAccess(sessionId, userId)

    const queryBuilder = this.conflictRepository.createQueryBuilder('conflict')
      .leftJoinAndSelect('conflict.user1', 'user1')
      .leftJoinAndSelect('conflict.user2', 'user2')
      .leftJoinAndSelect('conflict.operation1', 'operation1')
      .leftJoinAndSelect('conflict.operation2', 'operation2')
      .leftJoinAndSelect('conflict.resolver', 'resolver')
      .where('conflict.sessionId = :sessionId', { sessionId })

    if (query.type) {
      queryBuilder.andWhere('conflict.type = :type', { type: query.type })
    }

    if (query.status) {
      queryBuilder.andWhere('conflict.status = :status', { status: query.status })
    }

    if (query.targetType) {
      queryBuilder.andWhere('conflict.targetType = :targetType', { targetType: query.targetType })
    }

    if (query.targetId) {
      queryBuilder.andWhere('conflict.targetId = :targetId', { targetId: query.targetId })
    }

    if (query.userId) {
      queryBuilder.andWhere('(conflict.user1Id = :userId OR conflict.user2Id = :userId)', { userId: query.userId })
    }

    // 分页
    const page = query.page || 1
    const limit = query.limit || 20
    const skip = (page - 1) * limit

    queryBuilder.skip(skip).take(limit)

    // 排序
    const sortBy = query.sortBy || 'createdAt'
    const sortOrder = query.sortOrder || 'DESC'
    queryBuilder.orderBy(`conflict.${sortBy}`, sortOrder)

    const [conflicts, total] = await queryBuilder.getManyAndCount()

    return {
      data: conflicts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 获取冲突详情
   */
  async getConflict(id: string, userId: string): Promise<CollaborationConflict> {
    const conflict = await this.conflictRepository.findOne({
      where: { id },
      relations: ['session', 'user1', 'user2', 'operation1', 'operation2', 'resolver']
    })

    if (!conflict) {
      throw new NotFoundException('冲突不存在')
    }

    // 验证用户权限
    await this.checkSessionAccess(conflict.sessionId, userId)

    return conflict
  }

  /**
   * 解决冲突
   */
  async resolveConflict(id: string, resolveConflictDto: ResolveConflictDto, userId: string) {
    const conflict = await this.getConflict(id, userId)

    if (conflict.status === ConflictStatus.RESOLVED) {
      throw new ConflictException('冲突已解决')
    }

    // 检查权限：冲突相关用户或会话所有者可以解决
    if (!this.canResolveConflict(conflict, userId)) {
      throw new ForbiddenException('权限不足，无法解决此冲突')
    }

    try {
      // 应用解决方案
      await this.applyResolution(conflict, resolveConflictDto)

      // 更新冲突状态
      conflict.status = ConflictStatus.RESOLVED
      conflict.resolutionData = resolveConflictDto.resolutionData || {}
      conflict.resolvedBy = userId
      conflict.resolvedAt = new Date()

      await this.conflictRepository.save(conflict)

      return { success: true, message: '冲突解决成功' }
    } catch (error) {
      throw new ConflictException(`冲突解决失败: ${error.message}`)
    }
  }

  /**
   * 自动解决冲突
   */
  async autoResolveConflict(id: string, userId: string) {
    const conflict = await this.getConflict(id, userId)

    if (conflict.status === ConflictStatus.RESOLVED) {
      throw new ConflictException('冲突已解决')
    }

    // 检查是否可以自动解决
    const autoResolution = await this.generateAutoResolution(conflict)
    if (!autoResolution) {
      throw new ConflictException('此冲突无法自动解决')
    }

    try {
      // 应用自动解决方案
      await this.applyResolution(conflict, autoResolution)

      // 更新冲突状态
      conflict.status = ConflictStatus.RESOLVED
      conflict.resolutionData = autoResolution.resolutionData || {}
      conflict.resolvedBy = userId
      conflict.resolvedAt = new Date()

      await this.conflictRepository.save(conflict)

      return { 
        success: true, 
        message: '冲突自动解决成功',
        resolution: autoResolution.resolution
      }
    } catch (error) {
      throw new ConflictException(`自动解决冲突失败: ${error.message}`)
    }
  }

  /**
   * 获取冲突解决建议
   */
  async getConflictSuggestions(id: string, userId: string) {
    const conflict = await this.getConflict(id, userId)

    const suggestions = []

    // 基于冲突类型生成建议
    switch (conflict.type) {
      case ConflictType.CONCURRENT_EDIT:
        suggestions.push(
          { type: 'accept_mine', description: '保留我的修改', confidence: 0.6 },
          { type: 'accept_theirs', description: '保留对方的修改', confidence: 0.6 },
          { type: 'merge', description: '合并两个修改', confidence: 0.8 }
        )
        break
      case ConflictType.VERSION_MISMATCH:
        suggestions.push(
          { type: 'accept_latest', description: '使用最新版本', confidence: 0.9 },
          { type: 'manual_merge', description: '手动合并', confidence: 0.7 }
        )
        break
      case ConflictType.PERMISSION_CONFLICT:
        suggestions.push(
          { type: 'check_permissions', description: '重新检查权限', confidence: 0.8 }
        )
        break
      case ConflictType.RESOURCE_LOCK:
        suggestions.push(
          { type: 'wait_for_unlock', description: '等待资源解锁', confidence: 0.7 },
          { type: 'force_unlock', description: '强制解锁', confidence: 0.3 }
        )
        break
    }

    return {
      conflictId: id,
      suggestions,
      autoResolvable: await this.isAutoResolvable(conflict)
    }
  }

  /**
   * 获取用户相关冲突
   */
  async getUserConflicts(userId: string, query: ConflictQueryDto) {
    const queryBuilder = this.conflictRepository.createQueryBuilder('conflict')
      .leftJoinAndSelect('conflict.session', 'session')
      .leftJoinAndSelect('conflict.user1', 'user1')
      .leftJoinAndSelect('conflict.user2', 'user2')
      .where('(conflict.user1Id = :userId OR conflict.user2Id = :userId)', { userId })

    if (query.status) {
      queryBuilder.andWhere('conflict.status = :status', { status: query.status })
    }

    if (query.type) {
      queryBuilder.andWhere('conflict.type = :type', { type: query.type })
    }

    const page = query.page || 1
    const limit = query.limit || 20
    const skip = (page - 1) * limit

    queryBuilder.skip(skip).take(limit)
    queryBuilder.orderBy('conflict.createdAt', 'DESC')

    const [conflicts, total] = await queryBuilder.getManyAndCount()

    return {
      data: conflicts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 忽略冲突
   */
  async ignoreConflict(id: string, userId: string) {
    const conflict = await this.getConflict(id, userId)

    if (!this.canResolveConflict(conflict, userId)) {
      throw new ForbiddenException('权限不足，无法忽略此冲突')
    }

    conflict.status = ConflictStatus.REJECTED
    conflict.resolvedBy = userId
    conflict.resolvedAt = new Date()

    await this.conflictRepository.save(conflict)

    return { success: true, message: '冲突已忽略' }
  }

  /**
   * 获取冲突统计
   */
  async getConflictStats(sessionId: string, userId: string) {
    await this.checkSessionAccess(sessionId, userId)

    const stats = await this.conflictRepository
      .createQueryBuilder('conflict')
      .select('conflict.status, conflict.type, COUNT(*) as count')
      .where('conflict.sessionId = :sessionId', { sessionId })
      .groupBy('conflict.status, conflict.type')
      .getRawMany()

    const summary = {
      total: 0,
      pending: 0,
      resolved: 0,
      rejected: 0,
      byType: {}
    }

    stats.forEach(stat => {
      const count = parseInt(stat.count)
      summary.total += count
      summary[stat.status] += count
      
      if (!summary.byType[stat.type]) {
        summary.byType[stat.type] = 0
      }
      summary.byType[stat.type] += count
    })

    return summary
  }

  /**
   * 创建冲突记录
   */
  async createConflict(
    sessionId: string,
    type: ConflictType,
    targetType: string,
    targetId: string,
    user1Id: string,
    user2Id: string,
    description: string,
    conflictData: Record<string, any>,
    operation1Id?: string,
    operation2Id?: string
  ): Promise<CollaborationConflict> {
    const conflict = this.conflictRepository.create({
      sessionId,
      type,
      targetType,
      targetId,
      user1Id,
      user2Id,
      description,
      conflictData,
      operation1Id,
      operation2Id,
      status: ConflictStatus.PENDING
    })

    return await this.conflictRepository.save(conflict)
  }

  /**
   * 检查是否可以解决冲突
   */
  private canResolveConflict(conflict: CollaborationConflict, userId: string): boolean {
    // 冲突相关用户可以解决
    if (conflict.user1Id === userId || conflict.user2Id === userId) {
      return true
    }

    // TODO: 检查是否是会话所有者或管理员
    return false
  }

  /**
   * 应用解决方案
   */
  private async applyResolution(conflict: CollaborationConflict, resolution: ResolveConflictDto): Promise<void> {
    // TODO: 根据解决方案类型应用具体的解决逻辑
    switch (resolution.resolution) {
      case 'accept_mine':
        // 保留第一个用户的修改
        break
      case 'accept_theirs':
        // 保留第二个用户的修改
        break
      case 'merge':
        // 合并两个修改
        break
      case 'custom':
        // 应用自定义解决方案
        break
    }
  }

  /**
   * 生成自动解决方案
   */
  private async generateAutoResolution(conflict: CollaborationConflict): Promise<ResolveConflictDto | null> {
    // TODO: 基于冲突类型和数据生成自动解决方案
    switch (conflict.type) {
      case ConflictType.VERSION_MISMATCH:
        return {
          resolution: 'accept_theirs', // 使用最新版本
          resolutionData: { reason: 'auto_latest_version' }
        }
      default:
        return null
    }
  }

  /**
   * 检查是否可以自动解决
   */
  private async isAutoResolvable(conflict: CollaborationConflict): Promise<boolean> {
    const autoResolution = await this.generateAutoResolution(conflict)
    return autoResolution !== null
  }

  /**
   * 检查会话访问权限
   */
  private async checkSessionAccess(sessionId: string, userId: string): Promise<void> {
    const session = await this.sessionRepository.findOne({ where: { id: sessionId } })
    if (!session) {
      throw new NotFoundException('协作会话不存在')
    }
    // TODO: 实现权限检查逻辑
  }
}
