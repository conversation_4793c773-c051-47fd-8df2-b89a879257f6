import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { CollaborationSession } from './collaboration-session.entity'
import { CollaborationOperation } from './collaboration-operation.entity'

/**
 * 冲突状态枚举
 */
export enum ConflictStatus {
  PENDING = 'pending',
  RESOLVED = 'resolved',
  REJECTED = 'rejected'
}

/**
 * 冲突类型枚举
 */
export enum ConflictType {
  CONCURRENT_EDIT = 'concurrent_edit',
  VERSION_MISMATCH = 'version_mismatch',
  PERMISSION_CONFLICT = 'permission_conflict',
  RESOURCE_LOCK = 'resource_lock'
}

/**
 * 协作冲突实体
 * 
 * 记录和管理协作过程中的冲突
 */
@Entity('collaboration_conflicts')
export class CollaborationConflict {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ name: 'session_id' })
  sessionId: string

  @Column({ type: 'enum', enum: ConflictType })
  type: ConflictType

  @Column({ type: 'enum', enum: ConflictStatus, default: ConflictStatus.PENDING })
  status: ConflictStatus

  @Column({ name: 'target_type', type: 'varchar', length: 100 })
  targetType: string

  @Column({ name: 'target_id', type: 'varchar', length: 255 })
  targetId: string

  @Column({ name: 'operation1_id', nullable: true })
  operation1Id: string

  @Column({ name: 'operation2_id', nullable: true })
  operation2Id: string

  @Column({ name: 'user1_id' })
  user1Id: string

  @Column({ name: 'user2_id' })
  user2Id: string

  @Column({ type: 'text' })
  description: string

  @Column({ name: 'conflict_data', type: 'json' })
  conflictData: Record<string, any>

  @Column({ name: 'resolution_data', type: 'json', nullable: true })
  resolutionData: Record<string, any>

  @Column({ name: 'resolved_by', nullable: true })
  resolvedBy: string

  @Column({ name: 'resolved_at', type: 'timestamp', nullable: true })
  resolvedAt: Date

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date

  // 关联关系
  @ManyToOne(() => CollaborationSession, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'session_id' })
  session: CollaborationSession

  @ManyToOne(() => CollaborationOperation, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'operation1_id' })
  operation1: CollaborationOperation

  @ManyToOne(() => CollaborationOperation, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'operation2_id' })
  operation2: CollaborationOperation

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user1_id' })
  user1: User

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user2_id' })
  user2: User

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'resolved_by' })
  resolver: User
}
