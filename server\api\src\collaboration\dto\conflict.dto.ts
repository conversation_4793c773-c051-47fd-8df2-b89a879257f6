import { IsString, IsOptional, IsEnum, IsObject, IsUUID, IsNumber } from 'class-validator'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { ConflictStatus, ConflictType } from '../entities/collaboration-conflict.entity'

/**
 * 解决冲突DTO
 */
export class ResolveConflictDto {
  @ApiProperty({ description: '解决方案', enum: ['accept_mine', 'accept_theirs', 'merge', 'custom'] })
  @IsEnum(['accept_mine', 'accept_theirs', 'merge', 'custom'])
  resolution: 'accept_mine' | 'accept_theirs' | 'merge' | 'custom'

  @ApiPropertyOptional({ description: '解决数据' })
  @IsOptional()
  @IsObject()
  resolutionData?: Record<string, any>

  @ApiPropertyOptional({ description: '解决说明' })
  @IsOptional()
  @IsString()
  comment?: string
}

/**
 * 冲突查询DTO
 */
export class ConflictQueryDto {
  @ApiPropertyOptional({ description: '冲突类型', enum: ConflictType })
  @IsOptional()
  @IsEnum(ConflictType)
  type?: ConflictType

  @ApiPropertyOptional({ description: '冲突状态', enum: ConflictStatus })
  @IsOptional()
  @IsEnum(ConflictStatus)
  status?: ConflictStatus

  @ApiPropertyOptional({ description: '目标类型' })
  @IsOptional()
  @IsString()
  targetType?: string

  @ApiPropertyOptional({ description: '目标ID' })
  @IsOptional()
  @IsString()
  targetId?: string

  @ApiPropertyOptional({ description: '用户ID' })
  @IsOptional()
  @IsUUID()
  userId?: string

  @ApiPropertyOptional({ description: '页码', minimum: 1, default: 1 })
  @IsOptional()
  @IsNumber()
  page?: number

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 20 })
  @IsOptional()
  @IsNumber()
  limit?: number

  @ApiPropertyOptional({ description: '排序字段', default: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string

  @ApiPropertyOptional({ description: '排序方向', enum: ['ASC', 'DESC'], default: 'DESC' })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC'
}
