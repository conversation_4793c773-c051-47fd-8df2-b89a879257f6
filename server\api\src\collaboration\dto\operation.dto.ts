import { IsString, IsOptional, IsE<PERSON>, IsObject, IsUUID, IsNumber } from 'class-validator'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { OperationType, OperationStatus } from '../entities/collaboration-operation.entity'

/**
 * 创建协作操作DTO
 */
export class CreateOperationDto {
  @ApiProperty({ description: '会话ID' })
  @IsUUID()
  sessionId: string

  @ApiProperty({ description: '操作类型', enum: OperationType })
  @IsEnum(OperationType)
  type: OperationType

  @ApiProperty({ description: '目标类型' })
  @IsString()
  targetType: string

  @ApiProperty({ description: '目标ID' })
  @IsString()
  targetId: string

  @ApiProperty({ description: '操作数据' })
  @IsObject()
  data: Record<string, any>

  @ApiPropertyOptional({ description: '操作前数据' })
  @IsOptional()
  @IsObject()
  previousData?: Record<string, any>

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>
}

/**
 * 应用操作DTO
 */
export class ApplyOperationDto {
  @ApiPropertyOptional({ description: '应用选项' })
  @IsOptional()
  @IsObject()
  options?: Record<string, any>

  @ApiPropertyOptional({ description: '强制应用' })
  @IsOptional()
  force?: boolean
}

/**
 * 操作查询DTO
 */
export class OperationQueryDto {
  @ApiPropertyOptional({ description: '操作类型', enum: OperationType })
  @IsOptional()
  @IsEnum(OperationType)
  type?: OperationType

  @ApiPropertyOptional({ description: '操作状态', enum: OperationStatus })
  @IsOptional()
  @IsEnum(OperationStatus)
  status?: OperationStatus

  @ApiPropertyOptional({ description: '目标类型' })
  @IsOptional()
  @IsString()
  targetType?: string

  @ApiPropertyOptional({ description: '目标ID' })
  @IsOptional()
  @IsString()
  targetId?: string

  @ApiPropertyOptional({ description: '用户ID' })
  @IsOptional()
  @IsUUID()
  userId?: string

  @ApiPropertyOptional({ description: '起始操作序号' })
  @IsOptional()
  @IsNumber()
  fromOrder?: number

  @ApiPropertyOptional({ description: '结束操作序号' })
  @IsOptional()
  @IsNumber()
  toOrder?: number

  @ApiPropertyOptional({ description: '页码', minimum: 1, default: 1 })
  @IsOptional()
  @IsNumber()
  page?: number

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 20 })
  @IsOptional()
  @IsNumber()
  limit?: number

  @ApiPropertyOptional({ description: '排序字段', default: 'operationOrder' })
  @IsOptional()
  @IsString()
  sortBy?: string

  @ApiPropertyOptional({ description: '排序方向', enum: ['ASC', 'DESC'], default: 'ASC' })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC'
}
