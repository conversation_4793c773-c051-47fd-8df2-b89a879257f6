import { Injectable, Logger } from '@nestjs/common'
import { CollaborationConflict, ConflictType } from '../entities/collaboration-conflict.entity'
import { CollaborationOperation } from '../entities/collaboration-operation.entity'
import { OperationalTransformService } from './operational-transform.service'

/**
 * 冲突解决服务
 * 
 * 提供智能冲突检测和解决策略
 */
@Injectable()
export class ConflictResolutionService {
  private readonly logger = new Logger(ConflictResolutionService.name)

  constructor(
    private readonly operationalTransformService: OperationalTransformService
  ) {}

  /**
   * 检测操作冲突
   */
  async detectConflict(
    newOperation: CollaborationOperation,
    existingOperations: CollaborationOperation[]
  ): Promise<CollaborationConflict | null> {
    // 查找可能冲突的操作
    const conflictingOperations = existingOperations.filter(op => 
      this.isConflicting(newOperation, op)
    )

    if (conflictingOperations.length === 0) {
      return null
    }

    // 选择最相关的冲突操作
    const mostRelevantConflict = this.findMostRelevantConflict(newOperation, conflictingOperations)
    
    // 确定冲突类型
    const conflictType = this.determineConflictType(newOperation, mostRelevantConflict)
    
    // 生成冲突描述
    const description = this.generateConflictDescription(newOperation, mostRelevantConflict, conflictType)
    
    // 收集冲突数据
    const conflictData = this.collectConflictData(newOperation, mostRelevantConflict)

    return {
      sessionId: newOperation.sessionId,
      type: conflictType,
      targetType: newOperation.targetType,
      targetId: newOperation.targetId,
      user1Id: mostRelevantConflict.userId,
      user2Id: newOperation.userId,
      operation1Id: mostRelevantConflict.id,
      operation2Id: newOperation.id,
      description,
      conflictData,
      status: 'pending'
    } as CollaborationConflict
  }

  /**
   * 自动解决冲突
   */
  async autoResolveConflict(conflict: CollaborationConflict): Promise<{
    canResolve: boolean,
    resolution?: any,
    transformedOperations?: CollaborationOperation[]
  }> {
    switch (conflict.type) {
      case ConflictType.CONCURRENT_EDIT:
        return await this.resolveConcurrentEditConflict(conflict)
      
      case ConflictType.VERSION_MISMATCH:
        return await this.resolveVersionMismatchConflict(conflict)
      
      case ConflictType.PERMISSION_CONFLICT:
        return await this.resolvePermissionConflict(conflict)
      
      case ConflictType.RESOURCE_LOCK:
        return await this.resolveResourceLockConflict(conflict)
      
      default:
        return { canResolve: false }
    }
  }

  /**
   * 生成解决建议
   */
  async generateResolutionSuggestions(conflict: CollaborationConflict): Promise<Array<{
    type: string,
    description: string,
    confidence: number,
    data?: any
  }>> {
    const suggestions = []

    switch (conflict.type) {
      case ConflictType.CONCURRENT_EDIT:
        suggestions.push(
          {
            type: 'operational_transform',
            description: '使用操作转换自动合并',
            confidence: 0.8,
            data: { method: 'ot' }
          },
          {
            type: 'three_way_merge',
            description: '三方合并',
            confidence: 0.7,
            data: { method: 'merge' }
          },
          {
            type: 'manual_review',
            description: '手动审查解决',
            confidence: 0.9,
            data: { method: 'manual' }
          }
        )
        break

      case ConflictType.VERSION_MISMATCH:
        suggestions.push(
          {
            type: 'use_latest',
            description: '使用最新版本',
            confidence: 0.9,
            data: { strategy: 'latest' }
          },
          {
            type: 'rebase_changes',
            description: '重新基于最新版本应用更改',
            confidence: 0.8,
            data: { strategy: 'rebase' }
          }
        )
        break

      case ConflictType.PERMISSION_CONFLICT:
        suggestions.push(
          {
            type: 'check_permissions',
            description: '重新验证权限',
            confidence: 0.9,
            data: { action: 'recheck' }
          },
          {
            type: 'request_permission',
            description: '请求权限提升',
            confidence: 0.7,
            data: { action: 'request' }
          }
        )
        break

      case ConflictType.RESOURCE_LOCK:
        suggestions.push(
          {
            type: 'wait_for_unlock',
            description: '等待资源解锁',
            confidence: 0.8,
            data: { action: 'wait' }
          },
          {
            type: 'force_unlock',
            description: '强制解锁（需要管理员权限）',
            confidence: 0.3,
            data: { action: 'force' }
          }
        )
        break
    }

    return suggestions
  }

  /**
   * 应用解决方案
   */
  async applyResolution(
    conflict: CollaborationConflict,
    resolution: {
      type: string,
      data?: any
    }
  ): Promise<{
    success: boolean,
    result?: any,
    error?: string
  }> {
    try {
      switch (resolution.type) {
        case 'operational_transform':
          return await this.applyOperationalTransform(conflict)
        
        case 'three_way_merge':
          return await this.applyThreeWayMerge(conflict)
        
        case 'use_latest':
          return await this.applyUseLatest(conflict)
        
        case 'rebase_changes':
          return await this.applyRebaseChanges(conflict)
        
        case 'accept_mine':
          return await this.applyAcceptMine(conflict)
        
        case 'accept_theirs':
          return await this.applyAcceptTheirs(conflict)
        
        case 'manual_merge':
          return await this.applyManualMerge(conflict, resolution.data)
        
        default:
          return {
            success: false,
            error: `不支持的解决方案类型: ${resolution.type}`
          }
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 检查两个操作是否冲突
   */
  private isConflicting(op1: CollaborationOperation, op2: CollaborationOperation): boolean {
    // 不同目标不冲突
    if (op1.targetId !== op2.targetId || op1.targetType !== op2.targetType) {
      return false
    }

    // 同一用户的操作不冲突
    if (op1.userId === op2.userId) {
      return false
    }

    // 时间窗口检查（5分钟内的操作可能冲突）
    const timeDiff = Math.abs(op1.createdAt.getTime() - op2.createdAt.getTime())
    if (timeDiff > 5 * 60 * 1000) {
      return false
    }

    // 检查操作类型冲突
    return this.checkOperationTypeConflict(op1, op2)
  }

  /**
   * 检查操作类型冲突
   */
  private checkOperationTypeConflict(op1: CollaborationOperation, op2: CollaborationOperation): boolean {
    const conflictMatrix = {
      'CREATE': ['CREATE'],
      'UPDATE': ['UPDATE', 'DELETE'],
      'DELETE': ['UPDATE', 'DELETE'],
      'MOVE': ['MOVE', 'DELETE'],
      'COPY': [],
      'PASTE': ['UPDATE'],
      'UNDO': ['UPDATE', 'DELETE'],
      'REDO': ['UPDATE', 'DELETE']
    }

    const conflictingTypes = conflictMatrix[op1.type] || []
    return conflictingTypes.includes(op2.type)
  }

  /**
   * 查找最相关的冲突操作
   */
  private findMostRelevantConflict(
    newOperation: CollaborationOperation,
    conflictingOperations: CollaborationOperation[]
  ): CollaborationOperation {
    // 按时间距离排序，选择最近的
    return conflictingOperations.sort((a, b) => {
      const timeDiffA = Math.abs(newOperation.createdAt.getTime() - a.createdAt.getTime())
      const timeDiffB = Math.abs(newOperation.createdAt.getTime() - b.createdAt.getTime())
      return timeDiffA - timeDiffB
    })[0]
  }

  /**
   * 确定冲突类型
   */
  private determineConflictType(op1: CollaborationOperation, op2: CollaborationOperation): ConflictType {
    // 并发编辑冲突
    if (op1.type === 'UPDATE' && op2.type === 'UPDATE') {
      return ConflictType.CONCURRENT_EDIT
    }

    // 版本不匹配冲突
    if (op1.metadata?.version !== op2.metadata?.version) {
      return ConflictType.VERSION_MISMATCH
    }

    // 权限冲突
    if (op1.metadata?.requiresPermission || op2.metadata?.requiresPermission) {
      return ConflictType.PERMISSION_CONFLICT
    }

    // 资源锁冲突
    if (op1.metadata?.locked || op2.metadata?.locked) {
      return ConflictType.RESOURCE_LOCK
    }

    return ConflictType.CONCURRENT_EDIT
  }

  /**
   * 生成冲突描述
   */
  private generateConflictDescription(
    op1: CollaborationOperation,
    op2: CollaborationOperation,
    conflictType: ConflictType
  ): string {
    const targetDesc = `${op1.targetType} "${op1.targetId}"`
    
    switch (conflictType) {
      case ConflictType.CONCURRENT_EDIT:
        return `用户同时编辑了${targetDesc}`
      
      case ConflictType.VERSION_MISMATCH:
        return `${targetDesc}的版本不匹配`
      
      case ConflictType.PERMISSION_CONFLICT:
        return `对${targetDesc}的权限冲突`
      
      case ConflictType.RESOURCE_LOCK:
        return `${targetDesc}被锁定，无法同时操作`
      
      default:
        return `${targetDesc}发生未知冲突`
    }
  }

  /**
   * 收集冲突数据
   */
  private collectConflictData(op1: CollaborationOperation, op2: CollaborationOperation): Record<string, any> {
    return {
      operation1: {
        type: op1.type,
        data: op1.data,
        timestamp: op1.createdAt,
        user: op1.userId
      },
      operation2: {
        type: op2.type,
        data: op2.data,
        timestamp: op2.createdAt,
        user: op2.userId
      },
      conflictAreas: this.identifyConflictAreas(op1, op2)
    }
  }

  /**
   * 识别冲突区域
   */
  private identifyConflictAreas(op1: CollaborationOperation, op2: CollaborationOperation): any[] {
    const areas = []

    // 文本冲突区域
    if (op1.data.position !== undefined && op2.data.position !== undefined) {
      areas.push({
        type: 'text_range',
        range1: { start: op1.data.position, length: op1.data.length || 0 },
        range2: { start: op2.data.position, length: op2.data.length || 0 }
      })
    }

    // 属性冲突
    if (op1.data.property && op2.data.property && op1.data.property === op2.data.property) {
      areas.push({
        type: 'property',
        property: op1.data.property,
        value1: op1.data.value,
        value2: op2.data.value
      })
    }

    return areas
  }

  /**
   * 解决并发编辑冲突
   */
  private async resolveConcurrentEditConflict(conflict: CollaborationConflict): Promise<{
    canResolve: boolean,
    resolution?: any,
    transformedOperations?: CollaborationOperation[]
  }> {
    // 使用操作转换尝试自动解决
    if (conflict.operation1 && conflict.operation2) {
      const transformed = this.operationalTransformService.transform(
        conflict.operation1,
        conflict.operation2
      )

      return {
        canResolve: true,
        resolution: {
          type: 'operational_transform',
          description: '使用操作转换自动合并'
        },
        transformedOperations: [
          transformed.operation1Prime,
          transformed.operation2Prime
        ]
      }
    }

    return { canResolve: false }
  }

  /**
   * 解决版本不匹配冲突
   */
  private async resolveVersionMismatchConflict(conflict: CollaborationConflict): Promise<{
    canResolve: boolean,
    resolution?: any
  }> {
    return {
      canResolve: true,
      resolution: {
        type: 'use_latest',
        description: '使用最新版本'
      }
    }
  }

  /**
   * 解决权限冲突
   */
  private async resolvePermissionConflict(conflict: CollaborationConflict): Promise<{
    canResolve: boolean,
    resolution?: any
  }> {
    // 权限冲突通常需要人工干预
    return { canResolve: false }
  }

  /**
   * 解决资源锁冲突
   */
  private async resolveResourceLockConflict(conflict: CollaborationConflict): Promise<{
    canResolve: boolean,
    resolution?: any
  }> {
    // 检查锁是否已过期
    const lockExpired = this.checkLockExpiration(conflict)
    
    if (lockExpired) {
      return {
        canResolve: true,
        resolution: {
          type: 'auto_unlock',
          description: '锁已过期，自动解锁'
        }
      }
    }

    return { canResolve: false }
  }

  /**
   * 检查锁是否过期
   */
  private checkLockExpiration(conflict: CollaborationConflict): boolean {
    // TODO: 实现锁过期检查逻辑
    return false
  }

  // 以下是各种解决方案的实现方法
  private async applyOperationalTransform(conflict: CollaborationConflict) {
    // TODO: 实现操作转换应用逻辑
    return { success: true, result: 'Applied operational transform' }
  }

  private async applyThreeWayMerge(conflict: CollaborationConflict) {
    // TODO: 实现三方合并逻辑
    return { success: true, result: 'Applied three-way merge' }
  }

  private async applyUseLatest(conflict: CollaborationConflict) {
    // TODO: 实现使用最新版本逻辑
    return { success: true, result: 'Used latest version' }
  }

  private async applyRebaseChanges(conflict: CollaborationConflict) {
    // TODO: 实现变基逻辑
    return { success: true, result: 'Rebased changes' }
  }

  private async applyAcceptMine(conflict: CollaborationConflict) {
    // TODO: 实现接受我的更改逻辑
    return { success: true, result: 'Accepted mine' }
  }

  private async applyAcceptTheirs(conflict: CollaborationConflict) {
    // TODO: 实现接受他们的更改逻辑
    return { success: true, result: 'Accepted theirs' }
  }

  private async applyManualMerge(conflict: CollaborationConflict, data: any) {
    // TODO: 实现手动合并逻辑
    return { success: true, result: 'Applied manual merge' }
  }
}
