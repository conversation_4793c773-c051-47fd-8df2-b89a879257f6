import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Request,
  HttpStatus,
  HttpException
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard'
import { CollaborationConflictsService } from '../services/collaboration-conflicts.service'
import { ResolveConflictDto, ConflictQueryDto } from '../dto/conflict.dto'

/**
 * 协作冲突控制器
 * 
 * 管理协作过程中的冲突检测和解决
 */
@ApiTags('协作冲突')
@Controller('collaboration/conflicts')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CollaborationConflictsController {
  constructor(
    private readonly conflictsService: CollaborationConflictsService
  ) {}

  /**
   * 获取会话冲突列表
   */
  @Get('session/:sessionId')
  @ApiOperation({ summary: '获取会话冲突列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async getSessionConflicts(
    @Param('sessionId') sessionId: string,
    @Query() query: ConflictQueryDto,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.conflictsService.getSessionConflicts(sessionId, query, userId)
  }

  /**
   * 获取冲突详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取冲突详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '冲突不存在' })
  async getConflict(
    @Param('id') id: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.conflictsService.getConflict(id, userId)
  }

  /**
   * 解决冲突
   */
  @Put(':id/resolve')
  @ApiOperation({ summary: '解决冲突' })
  @ApiResponse({ status: 200, description: '解决成功' })
  @ApiResponse({ status: 404, description: '冲突不存在' })
  @ApiResponse({ status: 403, description: '权限不足' })
  @ApiResponse({ status: 400, description: '冲突已解决或解决方案无效' })
  async resolveConflict(
    @Param('id') id: string,
    @Body() resolveConflictDto: ResolveConflictDto,
    @Request() req: any
  ) {
    try {
      const userId = req.user.id
      return await this.conflictsService.resolveConflict(id, resolveConflictDto, userId)
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST)
    }
  }

  /**
   * 自动解决冲突
   */
  @Post(':id/auto-resolve')
  @ApiOperation({ summary: '自动解决冲突' })
  @ApiResponse({ status: 200, description: '自动解决成功' })
  @ApiResponse({ status: 404, description: '冲突不存在' })
  @ApiResponse({ status: 400, description: '冲突无法自动解决' })
  async autoResolveConflict(
    @Param('id') id: string,
    @Request() req: any
  ) {
    try {
      const userId = req.user.id
      return await this.conflictsService.autoResolveConflict(id, userId)
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST)
    }
  }

  /**
   * 获取冲突解决建议
   */
  @Get(':id/suggestions')
  @ApiOperation({ summary: '获取冲突解决建议' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '冲突不存在' })
  async getConflictSuggestions(
    @Param('id') id: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.conflictsService.getConflictSuggestions(id, userId)
  }

  /**
   * 获取用户相关冲突
   */
  @Get('user/my-conflicts')
  @ApiOperation({ summary: '获取用户相关冲突' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserConflicts(
    @Query() query: ConflictQueryDto,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.conflictsService.getUserConflicts(userId, query)
  }

  /**
   * 标记冲突为已忽略
   */
  @Put(':id/ignore')
  @ApiOperation({ summary: '忽略冲突' })
  @ApiResponse({ status: 200, description: '标记成功' })
  @ApiResponse({ status: 404, description: '冲突不存在' })
  @ApiResponse({ status: 403, description: '权限不足' })
  async ignoreConflict(
    @Param('id') id: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.conflictsService.ignoreConflict(id, userId)
  }

  /**
   * 获取冲突统计
   */
  @Get('session/:sessionId/stats')
  @ApiOperation({ summary: '获取会话冲突统计' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async getConflictStats(
    @Param('sessionId') sessionId: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.conflictsService.getConflictStats(sessionId, userId)
  }
}
