import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm'
import { User } from '../../users/entities/user.entity'
import { CollaborationSession } from './collaboration-session.entity'

/**
 * 操作类型枚举
 */
export enum OperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  MOVE = 'move',
  COPY = 'copy',
  PASTE = 'paste',
  UNDO = 'undo',
  REDO = 'redo'
}

/**
 * 操作状态枚举
 */
export enum OperationStatus {
  PENDING = 'pending',
  APPLIED = 'applied',
  REJECTED = 'rejected',
  CONFLICTED = 'conflicted'
}

/**
 * 协作操作实体
 * 
 * 记录协作过程中的所有操作
 */
@Entity('collaboration_operations')
export class CollaborationOperation {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ name: 'session_id' })
  sessionId: string

  @Column({ name: 'user_id' })
  userId: string

  @Column({ type: 'enum', enum: OperationType })
  type: OperationType

  @Column({ type: 'enum', enum: OperationStatus, default: OperationStatus.PENDING })
  status: OperationStatus

  @Column({ name: 'target_type', type: 'varchar', length: 100 })
  targetType: string

  @Column({ name: 'target_id', type: 'varchar', length: 255 })
  targetId: string

  @Column({ type: 'json' })
  data: Record<string, any>

  @Column({ name: 'previous_data', type: 'json', nullable: true })
  previousData: Record<string, any>

  @Column({ name: 'operation_order', type: 'bigint' })
  operationOrder: number

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  // 关联关系
  @ManyToOne(() => CollaborationSession, session => session.operations, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'session_id' })
  session: CollaborationSession

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User
}
