import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  Request,
  HttpStatus,
  HttpException
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard'
import { CollaborationOperationsService } from '../services/collaboration-operations.service'
import { CreateOperationDto, OperationQueryDto, ApplyOperationDto } from '../dto/operation.dto'

/**
 * 协作操作控制器
 * 
 * 管理协作过程中的操作同步
 */
@ApiTags('协作操作')
@Controller('collaboration/operations')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CollaborationOperationsController {
  constructor(
    private readonly operationsService: CollaborationOperationsService
  ) {}

  /**
   * 创建协作操作
   */
  @Post()
  @ApiOperation({ summary: '创建协作操作' })
  @ApiResponse({ status: 201, description: '操作创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 409, description: '操作冲突' })
  async createOperation(
    @Body() createOperationDto: CreateOperationDto,
    @Request() req: any
  ) {
    try {
      const userId = req.user.id
      return await this.operationsService.createOperation(createOperationDto, userId)
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST)
    }
  }

  /**
   * 获取会话操作列表
   */
  @Get('session/:sessionId')
  @ApiOperation({ summary: '获取会话操作列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async getSessionOperations(
    @Param('sessionId') sessionId: string,
    @Query() query: OperationQueryDto,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.operationsService.getSessionOperations(sessionId, query, userId)
  }

  /**
   * 获取操作详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取操作详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '操作不存在' })
  async getOperation(
    @Param('id') id: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.operationsService.getOperation(id, userId)
  }

  /**
   * 应用操作
   */
  @Put(':id/apply')
  @ApiOperation({ summary: '应用操作' })
  @ApiResponse({ status: 200, description: '应用成功' })
  @ApiResponse({ status: 404, description: '操作不存在' })
  @ApiResponse({ status: 409, description: '操作冲突' })
  async applyOperation(
    @Param('id') id: string,
    @Body() applyOperationDto: ApplyOperationDto,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.operationsService.applyOperation(id, applyOperationDto, userId)
  }

  /**
   * 撤销操作
   */
  @Put(':id/undo')
  @ApiOperation({ summary: '撤销操作' })
  @ApiResponse({ status: 200, description: '撤销成功' })
  @ApiResponse({ status: 404, description: '操作不存在' })
  @ApiResponse({ status: 400, description: '操作无法撤销' })
  async undoOperation(
    @Param('id') id: string,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.operationsService.undoOperation(id, userId)
  }

  /**
   * 获取操作历史
   */
  @Get('session/:sessionId/history')
  @ApiOperation({ summary: '获取操作历史' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async getOperationHistory(
    @Param('sessionId') sessionId: string,
    @Query() query: OperationQueryDto,
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.operationsService.getOperationHistory(sessionId, query, userId)
  }

  /**
   * 同步操作
   */
  @Post('session/:sessionId/sync')
  @ApiOperation({ summary: '同步操作' })
  @ApiResponse({ status: 200, description: '同步成功' })
  @ApiResponse({ status: 404, description: '会话不存在' })
  async syncOperations(
    @Param('sessionId') sessionId: string,
    @Body() syncData: { lastOperationId?: string },
    @Request() req: any
  ) {
    const userId = req.user.id
    return await this.operationsService.syncOperations(sessionId, syncData.lastOperationId, userId)
  }
}
