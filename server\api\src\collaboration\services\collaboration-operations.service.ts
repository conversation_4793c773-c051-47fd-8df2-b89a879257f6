import { Injectable, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { CollaborationOperation, OperationType, OperationStatus } from '../entities/collaboration-operation.entity'
import { CollaborationSession } from '../entities/collaboration-session.entity'
import { User } from '../../users/entities/user.entity'
import { CreateOperationDto, OperationQueryDto, ApplyOperationDto } from '../dto/operation.dto'

/**
 * 协作操作服务
 */
@Injectable()
export class CollaborationOperationsService {
  constructor(
    @InjectRepository(CollaborationOperation)
    private readonly operationRepository: Repository<CollaborationOperation>,
    @InjectRepository(CollaborationSession)
    private readonly sessionRepository: Repository<CollaborationSession>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}

  /**
   * 创建协作操作
   */
  async createOperation(createOperationDto: CreateOperationDto, userId: string): Promise<CollaborationOperation> {
    // 验证会话是否存在且活跃
    const session = await this.sessionRepository.findOne({ 
      where: { id: createOperationDto.sessionId } 
    })
    
    if (!session) {
      throw new NotFoundException('协作会话不存在')
    }

    if (session.status !== 'active') {
      throw new ConflictException('会话未激活，无法创建操作')
    }

    // 获取下一个操作序号
    const lastOperation = await this.operationRepository.findOne({
      where: { sessionId: createOperationDto.sessionId },
      order: { operationOrder: 'DESC' }
    })

    const operationOrder = lastOperation ? lastOperation.operationOrder + 1 : 1

    // 创建操作
    const operation = this.operationRepository.create({
      ...createOperationDto,
      userId,
      operationOrder,
      status: OperationStatus.PENDING
    })

    const savedOperation = await this.operationRepository.save(operation)

    // 检测冲突
    await this.detectConflicts(savedOperation)

    return savedOperation
  }

  /**
   * 获取会话操作列表
   */
  async getSessionOperations(sessionId: string, query: OperationQueryDto, userId: string) {
    // 验证用户权限
    await this.checkSessionAccess(sessionId, userId)

    const queryBuilder = this.operationRepository.createQueryBuilder('operation')
      .leftJoinAndSelect('operation.user', 'user')
      .where('operation.sessionId = :sessionId', { sessionId })

    if (query.type) {
      queryBuilder.andWhere('operation.type = :type', { type: query.type })
    }

    if (query.status) {
      queryBuilder.andWhere('operation.status = :status', { status: query.status })
    }

    if (query.targetType) {
      queryBuilder.andWhere('operation.targetType = :targetType', { targetType: query.targetType })
    }

    if (query.targetId) {
      queryBuilder.andWhere('operation.targetId = :targetId', { targetId: query.targetId })
    }

    if (query.userId) {
      queryBuilder.andWhere('operation.userId = :userId', { userId: query.userId })
    }

    if (query.fromOrder) {
      queryBuilder.andWhere('operation.operationOrder >= :fromOrder', { fromOrder: query.fromOrder })
    }

    if (query.toOrder) {
      queryBuilder.andWhere('operation.operationOrder <= :toOrder', { toOrder: query.toOrder })
    }

    // 分页
    const page = query.page || 1
    const limit = query.limit || 20
    const skip = (page - 1) * limit

    queryBuilder.skip(skip).take(limit)

    // 排序
    const sortBy = query.sortBy || 'operationOrder'
    const sortOrder = query.sortOrder || 'ASC'
    queryBuilder.orderBy(`operation.${sortBy}`, sortOrder)

    const [operations, total] = await queryBuilder.getManyAndCount()

    return {
      data: operations,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 获取操作详情
   */
  async getOperation(id: string, userId: string): Promise<CollaborationOperation> {
    const operation = await this.operationRepository.findOne({
      where: { id },
      relations: ['user', 'session']
    })

    if (!operation) {
      throw new NotFoundException('操作不存在')
    }

    // 验证用户权限
    await this.checkSessionAccess(operation.sessionId, userId)

    return operation
  }

  /**
   * 应用操作
   */
  async applyOperation(id: string, applyOperationDto: ApplyOperationDto, userId: string) {
    const operation = await this.getOperation(id, userId)

    if (operation.status === OperationStatus.APPLIED) {
      throw new ConflictException('操作已应用')
    }

    if (operation.status === OperationStatus.CONFLICTED && !applyOperationDto.force) {
      throw new ConflictException('操作存在冲突，需要先解决冲突或强制应用')
    }

    // 应用操作逻辑
    try {
      // TODO: 实现具体的操作应用逻辑
      await this.executeOperation(operation)

      operation.status = OperationStatus.APPLIED
      await this.operationRepository.save(operation)

      return { success: true, message: '操作应用成功' }
    } catch (error) {
      operation.status = OperationStatus.REJECTED
      await this.operationRepository.save(operation)
      throw new ConflictException(`操作应用失败: ${error.message}`)
    }
  }

  /**
   * 撤销操作
   */
  async undoOperation(id: string, userId: string) {
    const operation = await this.getOperation(id, userId)

    if (operation.status !== OperationStatus.APPLIED) {
      throw new ConflictException('只能撤销已应用的操作')
    }

    // 检查是否可以撤销
    if (!this.canUndoOperation(operation)) {
      throw new ConflictException('操作无法撤销')
    }

    try {
      // TODO: 实现撤销逻辑
      await this.revertOperation(operation)

      // 创建撤销操作记录
      const undoOperation = this.operationRepository.create({
        sessionId: operation.sessionId,
        userId,
        type: OperationType.UNDO,
        targetType: operation.targetType,
        targetId: operation.targetId,
        data: operation.previousData || {},
        previousData: operation.data,
        operationOrder: await this.getNextOperationOrder(operation.sessionId),
        status: OperationStatus.APPLIED,
        metadata: {
          originalOperationId: operation.id
        }
      })

      await this.operationRepository.save(undoOperation)

      return { success: true, message: '操作撤销成功' }
    } catch (error) {
      throw new ConflictException(`操作撤销失败: ${error.message}`)
    }
  }

  /**
   * 获取操作历史
   */
  async getOperationHistory(sessionId: string, query: OperationQueryDto, userId: string) {
    await this.checkSessionAccess(sessionId, userId)

    const queryBuilder = this.operationRepository.createQueryBuilder('operation')
      .leftJoinAndSelect('operation.user', 'user')
      .where('operation.sessionId = :sessionId', { sessionId })
      .andWhere('operation.status = :status', { status: OperationStatus.APPLIED })

    if (query.targetType) {
      queryBuilder.andWhere('operation.targetType = :targetType', { targetType: query.targetType })
    }

    if (query.targetId) {
      queryBuilder.andWhere('operation.targetId = :targetId', { targetId: query.targetId })
    }

    const page = query.page || 1
    const limit = query.limit || 50
    const skip = (page - 1) * limit

    queryBuilder.skip(skip).take(limit)
    queryBuilder.orderBy('operation.operationOrder', 'ASC')

    const [operations, total] = await queryBuilder.getManyAndCount()

    return {
      data: operations,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 同步操作
   */
  async syncOperations(sessionId: string, lastOperationId: string | undefined, userId: string) {
    await this.checkSessionAccess(sessionId, userId)

    let fromOrder = 1
    if (lastOperationId) {
      const lastOperation = await this.operationRepository.findOne({
        where: { id: lastOperationId }
      })
      if (lastOperation) {
        fromOrder = lastOperation.operationOrder + 1
      }
    }

    const operations = await this.operationRepository.find({
      where: {
        sessionId,
        operationOrder: fromOrder >= 1 ? fromOrder : 1
      },
      relations: ['user'],
      order: { operationOrder: 'ASC' },
      take: 100 // 限制同步数量
    })

    return {
      operations,
      hasMore: operations.length === 100
    }
  }

  /**
   * 检测操作冲突
   */
  private async detectConflicts(operation: CollaborationOperation): Promise<void> {
    // TODO: 实现冲突检测逻辑
    // 检查是否有其他用户对同一目标进行了操作
  }

  /**
   * 执行操作
   */
  private async executeOperation(operation: CollaborationOperation): Promise<void> {
    // TODO: 根据操作类型执行具体的操作
    switch (operation.type) {
      case OperationType.CREATE:
        // 创建操作逻辑
        break
      case OperationType.UPDATE:
        // 更新操作逻辑
        break
      case OperationType.DELETE:
        // 删除操作逻辑
        break
      default:
        throw new Error(`不支持的操作类型: ${operation.type}`)
    }
  }

  /**
   * 撤销操作
   */
  private async revertOperation(operation: CollaborationOperation): Promise<void> {
    // TODO: 实现操作撤销逻辑
  }

  /**
   * 检查是否可以撤销操作
   */
  private canUndoOperation(operation: CollaborationOperation): boolean {
    // TODO: 实现撤销检查逻辑
    return true
  }

  /**
   * 获取下一个操作序号
   */
  private async getNextOperationOrder(sessionId: string): Promise<number> {
    const lastOperation = await this.operationRepository.findOne({
      where: { sessionId },
      order: { operationOrder: 'DESC' }
    })
    return lastOperation ? lastOperation.operationOrder + 1 : 1
  }

  /**
   * 检查会话访问权限
   */
  private async checkSessionAccess(sessionId: string, userId: string): Promise<void> {
    const session = await this.sessionRepository.findOne({ where: { id: sessionId } })
    if (!session) {
      throw new NotFoundException('协作会话不存在')
    }
    // TODO: 实现权限检查逻辑
  }
}
